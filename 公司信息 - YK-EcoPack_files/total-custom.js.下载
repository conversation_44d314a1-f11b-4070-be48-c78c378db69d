/**
 * Total Custom JS
 *
 * @package Luzuk Premium
 *
 * Distributed under the MIT license - http://opensource.org/licenses/MIT
 */

jQuery(function($){

  if($('.ht-sticky-header').length > 0){
    $(window).scroll(function(){
      if($(window).scrollTop() > 200 ){
        $('#ht-masthead').addClass('ht-sticky');
      }else{
        $('#ht-masthead').removeClass('ht-sticky');
      }
    });
  }

  if($('#ht-bx-slider .ht-slide').length > 0){
    $('#ht-bx-slider').bxSlider({
        'pager':false,
        'auto' : true,
        'mode' : 'fade',
        'pause' : 5000,
    });
  }

  $('.ht-testimonial-slider').bxSlider({
      'controls' : true,
      'pager': false,
      'auto' : true,
      'pause' : 5000,
      'mode' : 'fade',
      'prevText' : '<i class="fa fa-angle-left" aria-hidden="true"></i>',
      'nextText' : '<i class="fa fa-angle-right" aria-hidden="true"></i>',
  });

  $(".ht_client_logo_slider").owlCarousel({
      autoPlay: 4000,
      items : 5,
      itemsDesktop : [1199,5],
      itemsDesktopSmall : [979,4],
      itemsTablet: [768,3],
      itemsMobile: [479,2],
      pagination : false
  });
    
  $('.ht-portfolio-image').nivoLightbox();
  
  $('.ht-menu > ul').superfish({
      delay:       500,                            // one second delay on mouseout
      animation:   {opacity:'show',height:'show'},  // fade-in and slide-down animation
      speed:       'fast',                          // faster animation speed
  });
    
  $('.ht-service-excerpt h5').click(function(){
      $(this).next('.ht-service-text').slideToggle();
      $(this).parents('.ht-service-post').toggleClass('ht-active');
  });
  
  $('.ht-service-icon').click(function(){
      $(this).next('.ht-service-excerpt').find('.ht-service-text').slideToggle();
      $(this).parent('.ht-service-post').toggleClass('ht-active');
  });

  $('.toggle-bar').click(function(){
      $(this).next('.ht-menu').slideToggle();
  });

  setTimeout(function(){
    $.stellar({
      horizontalScrolling: false, 
      responsive: true,
    });
  }, 3000 );
  
  $('.ht-team-counter-wrap').waypoint(function() {
      setTimeout(function() {
          $('.odometer1').html($('.odometer1').data('count'));
      }, 500);
      setTimeout(function() {
          $('.odometer2').html($('.odometer2').data('count'));
      }, 1000);
      setTimeout(function() {
          $('.odometer3').html($('.odometer3').data('count'));
      }, 1500);
      setTimeout(function() {
          $('.odometer4').html($('.odometer4').data('count'));
      }, 2000);
      }, {
      offset: 800,
      triggerOnce: true
  });

  if($('.ht-sticky-header').length > 0){
    var onpageOffset = 74;
  }else{
    onpageOffset = 0
  }

  $('.ht-sticky-header .ht-menu').onePageNav({
    currentClass: 'current',
    changeHash: false,
    scrollSpeed: 750,
    scrollThreshold: 0.1,
    scrollOffset: onpageOffset
  });

 // *only* if we have anchor on the url
  if(window.location.hash) {
      $('html, body').animate({
          scrollTop: $(window.location.hash).offset().top - onpageOffset
      }, 1000 );        
  }

  $(window).scroll(function(){
      if($(window).scrollTop() > 300){
          $('#ht-back-top').removeClass('ht-hide');
      }else{
          $('#ht-back-top').addClass('ht-hide');
      }
  });

  $('#ht-back-top').click(function(){
      $('html,body').animate({scrollTop:0},800);
  });

  if( $('.ht-portfolio-posts').length > 0 ){

  var first_class = $('.ht-portfolio-cat-name:first').data('filter');
  $('.ht-portfolio-cat-name:first').addClass('active');

  var $container = $('.ht-portfolio-posts').imagesLoaded( function() {
  
  $container.isotope({
      itemSelector: '.ht-portfolio',
      filter: first_class
  });
        
  var elems = $container.isotope('getFilteredItemElements');

  elems.forEach(function(item, index){
    if ( index == 0 || index == 4 ) {
      $( item ).addClass('wide');
      var bg = $(item).find('.ht-portfolio-image').attr('href');
      $( item ).find('.ht-portfolio-wrap').css('background-image', 'url('+bg+')');
    }else{
      $( item ).removeClass('wide');
    }
  });

  GetMasonary();

  setTimeout(function(){
    $container.isotope({
    itemSelector: '.ht-portfolio',
    filter: first_class,
  });
  },2000);

  $(window).on( 'resize', function () {
     GetMasonary();
  });

}); 

$('.ht-portfolio-cat-name-list').on( 'click', '.ht-portfolio-cat-name', function() {
  var filterValue = $(this).attr('data-filter');
  $container.isotope({ filter: filterValue });
       
    var elems = $container.isotope('getFilteredItemElements');
    
    elems.forEach(function(item, index){
      if ( index == 0 || index == 4 ) {
        $( item ).addClass('wide');
        var bg = $(item).find('.ht-portfolio-image').attr('href');
        $( item ).find('.ht-portfolio-wrap').css('background-image', 'url('+bg+')');
      }else{
        $( item ).removeClass('wide');
      }
    }); 

    GetMasonary();      
  
	  var filterValue = $(this).attr('data-filter');
	  $container.isotope({ filter: filterValue });
        
	  $('.ht-portfolio-cat-name').removeClass('active');
	  $(this).addClass('active');
});

function GetMasonary(){        
   var winWidth = window.innerWidth;   
   if (winWidth > 580) {
                   
      $container.find('.ht-portfolio').each(function () { 
          var image_width = $(this).find('img').width();
          if($(this).hasClass('wide')){
            $(this).find('.ht-portfolio-wrap').css( { 
                height : (image_width*2) + 15 + 'px'
            });
          }else{
            $(this).find('.ht-portfolio-wrap').css( { 
                height : image_width + 'px'
            });
          }
      }); 

  }else {
      $container.find('.ht-portfolio').each(function () { 
          var image_width = $(this).find('img').width();
          if($(this).hasClass('wide')){
            $(this).find('.ht-portfolio-wrap').css( { 
                height : (image_width*2) + 8 + 'px'
            });
          }else{
            $(this).find('.ht-portfolio-wrap').css( { 
                height : image_width + 'px'
            });
          }
      }); 
  }    
}

}

});

jQuery(document).ready(function(){
  //Examples of how to assign the Colorbox event to elements
  jQuery(".group1").colorbox({rel:'group1'});
  jQuery(".group2").colorbox({rel:'group2', transition:"fade"});
  jQuery(".group3").colorbox({rel:'group3', transition:"none", width:"75%", height:"75%"});
  jQuery(".group4").colorbox({rel:'group4', slideshow:true});
  jQuery(".ajax").colorbox();
  jQuery(".youtube").colorbox({iframe:true, innerWidth:640, innerHeight:390});
  jQuery(".vimeo").colorbox({iframe:true, innerWidth:500, innerHeight:409});
  jQuery(".iframe").colorbox({iframe:true, width:"80%", height:"80%"});
  jQuery(".inline").colorbox({inline:true, width:"50%"});
  jQuery(".callbacks").colorbox({
    onOpen:function(){ alert('onOpen: colorbox is about to open'); },
    onLoad:function(){ alert('onLoad: colorbox has started to load the targeted content'); },
    onComplete:function(){ alert('onComplete: colorbox has displayed the loaded content'); },
    onCleanup:function(){ alert('onCleanup: colorbox has begun the close process'); },
    onClosed:function(){ alert('onClosed: colorbox has completely closed'); }
  });

  jQuery('.non-retina').colorbox({rel:'group5', transition:'none'})
  jQuery('.retina').colorbox({rel:'group5', transition:'none', retinaImage:true, retinaUrl:true});
});

