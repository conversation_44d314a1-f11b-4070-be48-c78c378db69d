<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安装验证 - YK-EcoPack</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #c9593f;
            text-align: center;
            margin-bottom: 30px;
        }
        .section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .section h2 {
            color: #333;
            margin-bottom: 15px;
        }
        .file-check {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px;
            margin: 3px 0;
            background: #f9f9f9;
            border-radius: 3px;
            font-size: 14px;
        }
        .status {
            padding: 3px 8px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
            font-size: 12px;
        }
        .status.pass {
            background: #28a745;
        }
        .status.fail {
            background: #dc3545;
        }
        .status.checking {
            background: #ffc107;
            color: #333;
        }
        .summary {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .nav-links {
            text-align: center;
            margin-top: 20px;
        }
        .nav-links a {
            display: inline-block;
            margin: 5px 10px;
            padding: 10px 15px;
            background: #c9593f;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .nav-links a:hover {
            background: #a84832;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>YK-EcoPack 网站安装验证</h1>
        
        <div class="summary">
            <strong>验证状态：</strong>
            <span id="overall-status">检查中...</span>
            <br><br>
            <strong>说明：</strong>此页面会自动检查所有必需的文件是否已正确安装。绿色表示文件存在，红色表示文件缺失。
        </div>

        <div class="section">
            <h2>CSS 文件检查</h2>
            <div class="file-check">
                <span>Bootstrap CSS</span>
                <span class="status checking" id="bootstrap-css">检查中</span>
            </div>
            <div class="file-check">
                <span>Font Awesome CSS</span>
                <span class="status checking" id="fontawesome-css">检查中</span>
            </div>
            <div class="file-check">
                <span>Animate CSS</span>
                <span class="status checking" id="animate-css">检查中</span>
            </div>
            <div class="file-check">
                <span>Owl Carousel CSS</span>
                <span class="status checking" id="owl-css">检查中</span>
            </div>
            <div class="file-check">
                <span>自定义样式</span>
                <span class="status checking" id="custom-css">检查中</span>
            </div>
        </div>

        <div class="section">
            <h2>JavaScript 文件检查</h2>
            <div class="file-check">
                <span>jQuery</span>
                <span class="status checking" id="jquery-js">检查中</span>
            </div>
            <div class="file-check">
                <span>Bootstrap JS</span>
                <span class="status checking" id="bootstrap-js">检查中</span>
            </div>
            <div class="file-check">
                <span>Owl Carousel JS</span>
                <span class="status checking" id="owl-js">检查中</span>
            </div>
            <div class="file-check">
                <span>WOW.js</span>
                <span class="status checking" id="wow-js">检查中</span>
            </div>
            <div class="file-check">
                <span>自定义脚本</span>
                <span class="status checking" id="main-js">检查中</span>
            </div>
        </div>

        <div class="section">
            <h2>关键图片文件检查</h2>
            <div class="file-check">
                <span>公司Logo</span>
                <span class="status checking" id="logo-img">检查中</span>
            </div>
            <div class="file-check">
                <span>轮播图片 - 购物袋</span>
                <span class="status checking" id="slider1-img">检查中</span>
            </div>
            <div class="file-check">
                <span>轮播图片 - 厨余垃圾袋</span>
                <span class="status checking" id="slider2-img">检查中</span>
            </div>
            <div class="file-check">
                <span>轮播图片 - 宠物垃圾袋</span>
                <span class="status checking" id="slider3-img">检查中</span>
            </div>
            <div class="file-check">
                <span>产品图片 - 购物袋</span>
                <span class="status checking" id="product1-img">检查中</span>
            </div>
            <div class="file-check">
                <span>产品图片 - 厨余垃圾袋</span>
                <span class="status checking" id="product2-img">检查中</span>
            </div>
            <div class="file-check">
                <span>产品图片 - 宠物垃圾袋</span>
                <span class="status checking" id="product3-img">检查中</span>
            </div>
            <div class="file-check">
                <span>工厂图片 - 大门</span>
                <span class="status checking" id="factory-img">检查中</span>
            </div>
            <div class="file-check">
                <span>联系页面图片</span>
                <span class="status checking" id="contact-img">检查中</span>
            </div>
        </div>

        <div class="nav-links">
            <a href="index.html">访问主页</a>
            <a href="test.html">运行功能测试</a>
            <a href="about.html">公司信息</a>
            <a href="contact.html">联系我们</a>
        </div>
    </div>

    <script>
        // 文件检查函数
        function checkFile(url, elementId, type = 'other') {
            const element = document.getElementById(elementId);
            
            if (type === 'image') {
                const img = new Image();
                img.onload = function() {
                    element.textContent = '存在';
                    element.className = 'status pass';
                    updateOverallStatus();
                };
                img.onerror = function() {
                    element.textContent = '缺失';
                    element.className = 'status fail';
                    updateOverallStatus();
                };
                img.src = url;
            } else {
                // 对于CSS和JS文件，我们创建相应的元素来测试
                const testElement = type === 'css' ? document.createElement('link') : document.createElement('script');
                
                testElement.onload = function() {
                    element.textContent = '存在';
                    element.className = 'status pass';
                    updateOverallStatus();
                };
                
                testElement.onerror = function() {
                    element.textContent = '缺失';
                    element.className = 'status fail';
                    updateOverallStatus();
                };
                
                if (type === 'css') {
                    testElement.rel = 'stylesheet';
                    testElement.href = url;
                } else {
                    testElement.src = url;
                }
                
                document.head.appendChild(testElement);
            }
        }

        let totalChecks = 0;
        let completedChecks = 0;
        let passedChecks = 0;

        function updateOverallStatus() {
            completedChecks++;
            
            // 计算通过的检查数量
            const passElements = document.querySelectorAll('.status.pass');
            passedChecks = passElements.length;
            
            if (completedChecks === totalChecks) {
                const overallStatus = document.getElementById('overall-status');
                if (passedChecks === totalChecks) {
                    overallStatus.innerHTML = '<span style="color: #28a745; font-weight: bold;">✅ 所有文件检查通过！网站已准备就绪。</span>';
                } else {
                    overallStatus.innerHTML = `<span style="color: #dc3545; font-weight: bold;">❌ ${totalChecks - passedChecks} 个文件缺失。请检查部署指南。</span>`;
                }
            }
        }

        // 开始检查所有文件
        window.onload = function() {
            // 计算总检查数
            totalChecks = document.querySelectorAll('.status.checking').length;
            
            // CSS文件检查
            checkFile('assets/css/bootstrap.min.css', 'bootstrap-css', 'css');
            checkFile('assets/css/font-awesome.min.css', 'fontawesome-css', 'css');
            checkFile('assets/css/animate.css', 'animate-css', 'css');
            checkFile('assets/css/owl.carousel.min.css', 'owl-css', 'css');
            checkFile('assets/css/style.css', 'custom-css', 'css');
            
            // JavaScript文件检查
            checkFile('assets/js/jquery.min.js', 'jquery-js', 'js');
            checkFile('assets/js/bootstrap.bundle.min.js', 'bootstrap-js', 'js');
            checkFile('assets/js/owl.carousel.min.js', 'owl-js', 'js');
            checkFile('assets/js/wow.min.js', 'wow-js', 'js');
            checkFile('assets/js/main.js', 'main-js', 'js');
            
            // 图片文件检查
            checkFile('assets/images/logo.png', 'logo-img', 'image');
            checkFile('assets/images/slider/grocery-bags.jpg', 'slider1-img', 'image');
            checkFile('assets/images/slider/kitchen-garbage-bags.jpg', 'slider2-img', 'image');
            checkFile('assets/images/slider/pet-waste-bags.jpg', 'slider3-img', 'image');
            checkFile('assets/images/products/grocery-bags-small.jpg', 'product1-img', 'image');
            checkFile('assets/images/products/kitchen-garbage-bags-small.jpg', 'product2-img', 'image');
            checkFile('assets/images/products/pet-waste-bags-small.jpg', 'product3-img', 'image');
            checkFile('assets/images/factory/factory-gate.jpg', 'factory-img', 'image');
            checkFile('assets/images/contact/contact-image.jpg', 'contact-img', 'image');
        };
    </script>
</body>
</html>
