/* YK-EcoPack Custom Styles */

/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', sans-serif;
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header Styles */
.header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    z-index: 1000;
    background: #fff;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}


.header .Rqst-btn a {
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-decoration: none;
    font-weight: 300;
    font-size: 16px;
    margin-right: 1px;
    padding: 10px;
    color: #fff;
    transition: all 0.5s;
    border-radius: 0px;
    cursor: pointer;
    text-align: center;
    z-index: 9;
    max-width: 12em;
    word-break: break-all;
    background-color: #c9593f;
    border: none;
    float: left;
}
.header .Rqst-btn a:after {
  position: absolute;
  content: "";
  width: 100%;
  height: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  border-radius: 0px;
   background-color: #000;
/*background-image: linear-gradient(315deg, #4dccc6 0%, #96e4df 74%);*/
  transition: all 0.3s ease;
}
.header .Rqst-btn a:hover {
  color: #c9593f;
}
.header .Rqst-btn a:hover:after {
  top: 0;
  height: 100%;
}
.header .Rqst-btn a:active {
  top: 2px;
}

.top-bar {
    background: #f8f9fa;
    padding: 10px 0;
}

.social-links a {
    color: #666;
    margin-right: 15px;
    font-size: 16px;
    transition: color 0.3s;
}

.social-links a:hover {
    color: #c9593f;
}

.main-header {
    padding: 15px 0;
}

.logo img {
    max-height: 60px;
}

.tagline {
    font-size: 14px;
    color: #666;
    margin: 0;
}

.navbar-nav .nav-link {
    color: #333;
    font-weight: 500;
    margin: 0 15px;
    transition: color 0.3s;
}

.navbar-nav .nav-link:hover,
.navbar-nav .nav-link.active {
    color: #c9593f;
}

.language-switcher .btn {
    margin: 0 5px;
    font-size: 12px;
}

/* Hero Slider */
.hero-slider {
    margin-top: 120px;
    position: relative;
    z-index: 1;
    margin-bottom: 0;
}

.hero-slide {
    position: relative;
    overflow: hidden;
}

.hero-slider .item img {
    width: 100%;
    height: 600px;
    object-fit: cover;
}

.hero-content {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    align-items: center;
    background: rgba(0, 0, 0, 0.3);
}

.hero-text {
    color: white;
    padding: 40px 0;
}

.hero-text h1 {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-text h2 {
    font-size: 32px;
    font-weight: 600;
    margin-bottom: 20px;
    color: #4CAF50;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
}

.hero-text p {
    font-size: 18px;
    margin-bottom: 30px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
}

.hero-text .btn {
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    text-transform: uppercase;
    border-radius: 30px;
}

/* Section Styles */
.section-title {
    margin-bottom: 50px;
}

.section-title h2 {
    font-size: 36px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

.border-line {
    width: 60px;
    height: 3px;
    background: #c9593f;
    margin: 0 auto;
}

/* Products Section */
.products-section {
    padding: 80px 0;
    position: relative;
    z-index: 2;
    background: #fff;
}

/*----------------------------------------
/* Blog Area (Product Cards)
/*----------------------------------------*/
.blog-area {
    position: relative;
    overflow: hidden;
}

.blog-area .blog-post {
    margin-bottom: 1.5em;
}

.blog-area li {
    list-style: none;
}

.blog-area .blog-single {
    padding: 1.2em 1em 1em 1em;
    position: relative;
    overflow: hidden;
    transition: all .5s;
    border: 1px solid #ddd;
    border-top: none;
    background: #fff;
}

.blog-area .inner-area-title {
    position: relative;
    font-size: 18px;
    font-weight: 700;
    word-break: break-word;
    padding: 0 0 0em;
    line-height: 30px;
    margin: 15px 0;
    color: #333;
}

.blog-area .inner-area-title:hover {
    color: #c9593f;
}

.blog-area .section-area-text {
    font-size: 16px;
    margin-bottom: 0;
    font-weight: 400;
    margin-top: 12px;
    color: #666;
    line-height: 1.6;
    min-height: 130px;
}

.blog-area .blog-thumbnail {
    position: relative;
    z-index: 0;
    overflow: hidden;
}

.blog-area .blog-thumbnail img {
    width: 100%;
    height: 250px;
    object-fit: cover;
    transition: all .5s ease;
}

.blog-area .blog-thumbnail:before,
.blog-area .blog-thumbnail:after {
    content: "";
    background-color: rgba(201, 89, 63, 0.3);
    height: 100%;
    width: 100%;
    opacity: 0.7;
    position: absolute;
    top: 0;
    left: -100%;
    z-index: 0;
    transition: all 0.4s ease-out 0.1s;
}

.blog-area .blog-post:hover .blog-thumbnail:after {
    opacity: 0;
    transform: scale(0.9, 0.7);
    left: 0;
    transition: all 0.3s ease-out 0s;
}

.blog-area .blog-post:hover .blog-thumbnail:before {
    left: 100%;
}

.blog-area .blog-post:hover .blog-thumbnail:after {
    opacity: 0.3;
    transform: scale(1);
}

/*=========button===========*/
.blog-area .btn5 a {
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    padding: 8px 12px;
    color: #fff;
    transition: all 0.5s;
    border-radius: 0px;
    cursor: pointer;
    text-align: center;
    z-index: 9;
    word-break: break-all;
    background-color: #c9593f;
    border: none;
    margin-top: 1.5em;
}

.blog-area .btn5 a:after {
    position: absolute;
    content: "";
    width: 100%;
    height: 0;
    bottom: 0;
    left: 0;
    z-index: -1;
    border-radius: 0px;
    background-color: #000000;
    transition: all 0.3s ease;
}

.blog-area .btn5 a:hover {
    color: #fff;
    text-decoration: none;
}

.blog-area .btn5 a:hover:after {
    top: 0;
    height: 100%;
}

.blog-area .btn5 a:active {
    top: 2px;
}

.blog-area .btn5 a i {
    padding: 5px 10px;
    color: #000000;
    background: #FFFFFF;
    margin-left: 8px;
    font-size: 16px;
    font-weight: 300;
    display: inline-block;
    vertical-align: middle;
}

/* Product Price Styling */
.product-price {
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border-left: 4px solid #c9593f;
}

.product-price .price-label {
    font-size: 14px;
    color: #666;
    margin-right: 10px;
}

.product-price .price {
    font-size: 18px;
    font-weight: 600;
    color: #c9593f;
}

/* Utility Classes */
.pd-0 {
    padding: 0;
}

.clearfix::after {
    content: "";
    display: table;
    clear: both;
}

.btn-primary {
    background: #c9593f;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    font-weight: 500;
    transition: all 0.3s;
}

.btn-primary:hover {
    background: #a84832;
    transform: translateY(-2px);
}

/* Factory Gallery */
.factory-gallery {
    padding: 80px 0;
    background: #f8f9fa;
}

.gallery-item {
    position: relative;
    overflow: hidden;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
}

.gallery-item:hover {
    transform: scale(1.05);
}

.gallery-item img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.gallery-caption {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.7);
    color: #fff;
    padding: 15px;
    text-align: center;
}

/* Services Section */
.services-section {
    padding: 80px 0;
}

.service-card {
    background: #fff;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
    text-align: center;
    transition: transform 0.3s;
    height: 100%;
}

.service-card:hover {
    transform: translateY(-5px);
}

.service-icon {
    margin-bottom: 30px;
}

.service-icon img {
    max-width: 80px;
    height: auto;
}

.service-content h4 {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.service-content p {
    color: #666;
    line-height: 1.6;
}

/* Certifications Section */
.certifications-section {
    padding: 80px 0;
    background: #fff;
}

.certification-content h3 {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

.certification-list {
    list-style: none;
    padding: 0;
    margin: 30px 0;
}

.certification-list li {
    padding: 10px 0;
    font-size: 16px;
    color: #666;
}

.certification-list li i {
    color: #4CAF50;
    margin-right: 10px;
    font-size: 18px;
}

.certification-image img {
    max-width: 300px;
    height: auto;
}

/* Product Features Section */
.product-features {
    padding: 80px 0;
}

.feature-card {
    padding: 30px 20px;
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s, box-shadow 0.3s;
    height: 100%;
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}

.feature-icon {
    margin-bottom: 20px;
}

.feature-icon img {
    width: 80px;
    height: 80px;
    object-fit: contain;
}

.feature-card h5 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 15px;
}

.feature-card p {
    color: #666;
    line-height: 1.6;
}

/* Company Info Section */
.company-info {
    padding: 80px 0;
    background: #f8f9fa;
}

.company-content h3 {
    font-size: 32px;
    font-weight: 600;
    color: #333;
    margin-bottom: 30px;
}

.company-content h4 {
    font-size: 24px;
    font-weight: 600;
    color: #c9593f;
    margin-bottom: 15px;
    margin-top: 25px;
}

.company-content h4:first-of-type {
    margin-top: 0;
}

.company-stats {
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.stat-item {
    text-align: center;
}

.stat-item h5 {
    font-size: 36px;
    font-weight: 700;
    color: #c9593f;
    margin-bottom: 10px;
}

.stat-item p {
    color: #666;
    margin: 0;
}

/* Footer */
.footer {
    background: #fff;
    border-top: 1px solid #eee;
}

.footer-widget h5 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 25px;
}

.contact-info p {
    margin-bottom: 10px;
    color: #666;
}

.contact-info i {
    color: #c9593f;
    margin-right: 10px;
    width: 20px;
}

.footer-links {
    list-style: none;
}

.footer-links li {
    margin-bottom: 10px;
}

.footer-links a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s;
}

.footer-links a:hover {
    color: #c9593f;
}

.footer .social-links a {
    display: inline-block;
    width: 40px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    background: #c9593f;
    color: #fff;
    border-radius: 50%;
    margin-right: 10px;
    transition: background 0.3s;
}

.footer .social-links a:hover {
    background: #a84832;
}

.footer-bottom {
    border-top: 1px solid #eee;
    padding-top: 30px;
    margin-top: 40px;
}

/* Back to Top */
.back-to-top {
    position: fixed;
    bottom: 30px;
    right: 30px;
    width: 50px;
    height: 50px;
    background: #c9593f;
    color: #fff;
    text-align: center;
    line-height: 50px;
    border-radius: 50%;
    display: none;
    transition: all 0.3s;
    z-index: 999;
}

.back-to-top:hover {
    background: #a84832;
    color: #fff;
    text-decoration: none;
}

/* Responsive Design */
@media (max-width: 768px) {
    .hero-slider {
        margin-top: 80px;
    }

    .hero-slider .item img {
        height: 300px;
    }

    .section-title h2 {
        font-size: 28px;
    }

    .product-content,
    .service-card {
        padding: 20px;
    }

    .gallery-item img {
        height: 150px;
    }
}

@media (max-width: 576px) {
    .main-header {
        padding: 10px 0;
    }

    .logo img {
        max-height: 40px;
    }

    .section-title h2 {
        font-size: 24px;
    }

    .hero-slider .item img {
        height: 250px;
    }
}

/* Animation Classes */
.animate-fade-in {
    opacity: 0;
    transform: translateY(30px);
    transition: all 0.6s ease;
}

.animate-fade-in.animated {
    opacity: 1;
    transform: translateY(0);
}

/* Page Header */
.page-header {
    background: linear-gradient(135deg, #8cc63f 0%, #c9593f 100%);
    color: #fff;
    padding: 150px 0 80px;
    margin-top: 120px;
}

.page-title {
    font-size: 48px;
    font-weight: 700;
    margin-bottom: 20px;
}

.breadcrumb {
    background: transparent;
    padding: 0;
    margin: 0;
}

.breadcrumb-item a {
    color: rgba(255, 255, 255, 0.8);
    text-decoration: none;
}

.breadcrumb-item.active {
    color: #fff;
}

/* Product Pages */
.product-hero {
    padding: 60px 0;
}

.product-hero-image img {
    max-height: 400px;
    object-fit: cover;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
}

.product-details {
    padding: 80px 0;
}

.product-content h2 {
    font-size: 32px;
    font-weight: 600;
    color: #333;
}

.product-content h3 {
    font-size: 24px;
    font-weight: 500;
}

.product-description p {
    font-size: 16px;
    line-height: 1.8;
    color: #666;
    margin-bottom: 20px;
}

.feature-list {
    list-style: none;
    padding: 0;
}

.feature-list li {
    padding: 8px 0;
    font-size: 16px;
    color: #666;
}

.feature-list i {
    margin-right: 10px;
}

.product-sidebar {
    padding-left: 30px;
}

.product-info-card,
.contact-card {
    background: #f8f9fa;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.product-info-card h4,
.contact-card h4 {
    font-size: 20px;
    font-weight: 600;
    color: #333;
    margin-bottom: 20px;
}

.info-item {
    display: flex;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid #eee;
}

.info-item:last-child {
    border-bottom: none;
}

.contact-card .contact-info p {
    margin-bottom: 10px;
    color: #666;
}

.contact-card .contact-info i {
    color: #c9593f;
    margin-right: 10px;
    width: 20px;
}

.btn-block {
    width: 100%;
    margin-top: 20px;
}

/* Process Cards */
.process-card {
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    transition: transform 0.3s;
    height: 100%;
}

.process-card:hover {
    transform: translateY(-5px);
}

.process-card img {
    width: 100%;
    height: 150px;
    object-fit: cover;
    border-radius: 8px;
}

.process-card h5 {
    font-size: 18px;
    font-weight: 600;
    color: #333;
    margin: 15px 0 10px;
}

.process-card p {
    color: #666;
    font-size: 14px;
}

/* Step Numbers for About Page */
.step-number {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: #c9593f;
    color: #fff;
    border-radius: 50%;
    line-height: 40px;
    text-align: center;
    font-weight: 600;
    margin-bottom: 20px;
}

.process-step img {
    width: 100%;
    height: 200px;
    object-fit: cover;
    border-radius: 10px;
}

/* Company Features */
.feature-item {
    display: flex;
    align-items: center;
    margin-bottom: 15px;
}

.feature-item i {
    margin-right: 15px;
    font-size: 18px;
}

.feature-item span {
    font-size: 16px;
    color: #666;
}

/* Contact Page Styles */
.contact-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.contact-item img {
    margin-right: 15px;
}

.contact-link {
    font-size: 18px;
    font-weight: 500;
    color: #333;
    text-decoration: none;
}

.contact-link:hover {
    color: #c9593f;
}

.social-link {
    display: block;
    color: #666;
    text-decoration: none;
    margin-bottom: 10px;
    transition: color 0.3s;
}

.social-link:hover {
    color: #c9593f;
}

.social-link i {
    margin-right: 10px;
    width: 20px;
}

.contact-form {
    background: #fff;
    padding: 40px;
    border-radius: 10px;
    box-shadow: 0 5px 20px rgba(0, 0, 0, 0.1);
}

.form-group label {
    font-weight: 500;
    color: #333;
    margin-bottom: 8px;
}

.form-control {
    border: 2px solid #eee;
    border-radius: 8px;
    padding: 12px 15px;
    font-size: 16px;
    transition: border-color 0.3s;
}

.form-control:focus {
    border-color: #c9593f;
    box-shadow: 0 0 0 0.2rem rgba(201, 89, 63, 0.25);
}

.form-control.error {
    border-color: #dc3545;
}

/* Lightbox Styles */
.lightbox-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.lightbox-content {
    position: relative;
    max-width: 90%;
    max-height: 90%;
}

.lightbox-content img {
    max-width: 100%;
    max-height: 100%;
    border-radius: 10px;
}

.lightbox-close {
    position: absolute;
    top: -40px;
    right: -40px;
    background: #fff;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 20px;
    cursor: pointer;
    color: #333;
}

/* Hero Slider Owl Carousel Custom Styles */
#hero-carousel.owl-carousel {
    display: block;
}

.hero-slider .owl-theme .owl-dots {
    position: absolute;
    bottom: 50%;
    right: 0px;
    display: none;
}

.hero-slider .owl-nav {
    display: block;
}

.hero-slider .owl-nav .owl-prev {
    position: absolute;
    left: 90px;
    top: 45%;
    opacity: 0;
    -webkit-transition: all 0.4s ease-out;
    transition: all 0.4s ease-out;
    background: rgba(0, 0, 0, 0.3) !important;
    color: #ffffff !important;
    width: 50px;
    line-height: 50px;
    height: 50px;
    display: block;
    z-index: 1000;
    border-radius: 0%;
    cursor: pointer;
    border: none;
}

.hero-slider .owl-nav .owl-next {
    position: absolute;
    right: 90px;
    top: 45%;
    opacity: 0;
    -webkit-transition: all 0.4s ease-out;
    transition: all 0.4s ease-out;
    background: rgba(0, 0, 0, 0.3) !important;
    color: #ffffff !important;
    width: 50px;
    height: 50px;
    line-height: 50px;
    display: block;
    z-index: 1000;
    border-radius: 0%;
    cursor: pointer;
    border: none;
}

.hero-slider .owl-nav .owl-prev span,
.hero-slider .owl-nav .owl-next span {
    font-size: 65px;
    color: #fff;
    line-height: 45px;
    font-weight: 300;
}

.hero-slider .owl-nav .owl-prev:focus,
.hero-slider .owl-nav .owl-next:focus {
    outline: 0;
}

.hero-slider .owl-nav .owl-prev:hover,
.hero-slider .owl-nav .owl-next:hover {
    background: #FFFFFF !important;
    color: #000000 !important;
}

.hero-slider:hover .owl-prev {
    left: 0px;
    opacity: 1;
}

.hero-slider:hover .owl-next {
    right: 0px;
    opacity: 1;
}

/* General Owl Carousel Styles */
.owl-nav {
    position: absolute;
    top: 50%;
    width: 100%;
    transform: translateY(-50%);
}

.owl-nav button {
    position: absolute;
    background: rgba(255, 255, 255, 0.8);
    border: none;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 20px;
    color: #333;
    transition: all 0.3s;
}

.owl-nav .owl-prev {
    left: 20px;
}

.owl-nav .owl-next {
    right: 20px;
}

.owl-nav button:hover {
    background: #c9593f;
    color: #fff;
}

.owl-dots {
    text-align: center;
    margin-top: 20px;
}

.owl-dots .owl-dot {
    display: inline-block;
    width: 12px;
    height: 12px;
    background: #ddd;
    border-radius: 50%;
    margin: 0 5px;
    transition: background 0.3s;
}

.owl-dots .owl-dot.active {
    background: #c9593f;
}