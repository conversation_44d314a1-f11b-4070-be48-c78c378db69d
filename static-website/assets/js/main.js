// YK-EcoPack Main JavaScript

$(document).ready(function() {
    
    // Initialize Hero Carousel
    $('#hero-carousel').owlCarousel({
        items: 1,
        loop: true,
        autoplay: true,
        autoplayTimeout: 5000,
        autoplayHoverPause: true,
        nav: true,
        dots: true,
        navText: ['<i class="fa fa-chevron-left"></i>', '<i class="fa fa-chevron-right"></i>'],
        responsive: {
            0: {
                nav: false
            },
            768: {
                nav: true
            }
        }
    });
    
    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if (target.length) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });
    
    // Back to top button
    $(window).scroll(function() {
        if ($(this).scrollTop() > 100) {
            $('#back-to-top').fadeIn();
        } else {
            $('#back-to-top').fadeOut();
        }
    });
    
    $('#back-to-top').click(function(e) {
        e.preventDefault();
        $('html, body').animate({
            scrollTop: 0
        }, 800);
    });
    
    // Navbar scroll effect
    $(window).scroll(function() {
        if ($(this).scrollTop() > 50) {
            $('.header').addClass('scrolled');
        } else {
            $('.header').removeClass('scrolled');
        }
    });
    
    // Initialize WOW.js for animations
    if (typeof WOW !== 'undefined') {
        new WOW().init();
    }
    
    // Mobile menu toggle
    $('.navbar-toggler').click(function() {
        $(this).toggleClass('active');
    });
    
    // Close mobile menu when clicking on a link
    $('.navbar-nav .nav-link').click(function() {
        if ($(window).width() < 992) {
            $('.navbar-collapse').collapse('hide');
            $('.navbar-toggler').removeClass('active');
        }
    });
    
    // Product cards hover effect
    $('.product-card').hover(
        function() {
            $(this).find('.product-image img').css('transform', 'scale(1.1)');
        },
        function() {
            $(this).find('.product-image img').css('transform', 'scale(1)');
        }
    );
    
    // Gallery lightbox effect (if needed)
    $('.gallery-item img').click(function() {
        var src = $(this).attr('src');
        var alt = $(this).attr('alt');
        
        // Create lightbox modal
        var lightbox = $('<div class="lightbox-overlay">' +
            '<div class="lightbox-content">' +
            '<img src="' + src + '" alt="' + alt + '">' +
            '<button class="lightbox-close">&times;</button>' +
            '</div>' +
            '</div>');
        
        $('body').append(lightbox);
        lightbox.fadeIn();
        
        // Close lightbox
        lightbox.click(function(e) {
            if (e.target === this || $(e.target).hasClass('lightbox-close')) {
                lightbox.fadeOut(function() {
                    lightbox.remove();
                });
            }
        });
    });
    
    // Form validation (if contact forms are added)
    $('form').submit(function(e) {
        var isValid = true;
        
        $(this).find('input[required], textarea[required]').each(function() {
            if ($(this).val().trim() === '') {
                $(this).addClass('error');
                isValid = false;
            } else {
                $(this).removeClass('error');
            }
        });
        
        // Email validation
        var emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        $(this).find('input[type="email"]').each(function() {
            if ($(this).val() && !emailRegex.test($(this).val())) {
                $(this).addClass('error');
                isValid = false;
            }
        });
        
        if (!isValid) {
            e.preventDefault();
            alert('请填写所有必填字段并确保邮箱格式正确。');
        }
    });
    
    // Lazy loading for images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });
        
        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }
    
    // Scroll animations
    $(window).scroll(function() {
        $('.animate-fade-in').each(function() {
            var elementTop = $(this).offset().top;
            var elementBottom = elementTop + $(this).outerHeight();
            var viewportTop = $(window).scrollTop();
            var viewportBottom = viewportTop + $(window).height();
            
            if (elementBottom > viewportTop && elementTop < viewportBottom) {
                $(this).addClass('animated');
            }
        });
    });
    
    // Counter animation (if needed)
    function animateCounter(element, target) {
        var current = 0;
        var increment = target / 100;
        var timer = setInterval(function() {
            current += increment;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            element.text(Math.floor(current));
        }, 20);
    }
    
    // Initialize counters when they come into view
    $('.counter').each(function() {
        var $this = $(this);
        var target = parseInt($this.data('target'));
        
        $(window).scroll(function() {
            var elementTop = $this.offset().top;
            var viewportBottom = $(window).scrollTop() + $(window).height();
            
            if (elementTop < viewportBottom && !$this.hasClass('animated')) {
                $this.addClass('animated');
                animateCounter($this, target);
            }
        });
    });
    
    // Preloader (if needed)
    $(window).on('load', function() {
        $('.preloader').fadeOut('slow');
    });
    
    // Language switcher functionality
    $('.language-switcher a').click(function(e) {
        e.preventDefault();
        var lang = $(this).text();
        
        // Here you would implement actual language switching logic
        // For now, just show an alert
        alert('语言切换功能将在后续版本中实现');
    });
    
    // Social media sharing (if needed)
    $('.share-btn').click(function(e) {
        e.preventDefault();
        var url = encodeURIComponent(window.location.href);
        var title = encodeURIComponent(document.title);
        var platform = $(this).data('platform');
        var shareUrl = '';
        
        switch(platform) {
            case 'facebook':
                shareUrl = 'https://www.facebook.com/sharer/sharer.php?u=' + url;
                break;
            case 'twitter':
                shareUrl = 'https://twitter.com/intent/tweet?url=' + url + '&text=' + title;
                break;
            case 'linkedin':
                shareUrl = 'https://www.linkedin.com/sharing/share-offsite/?url=' + url;
                break;
        }
        
        if (shareUrl) {
            window.open(shareUrl, '_blank', 'width=600,height=400');
        }
    });
    
});

// Additional utility functions
function debounce(func, wait, immediate) {
    var timeout;
    return function() {
        var context = this, args = arguments;
        var later = function() {
            timeout = null;
            if (!immediate) func.apply(context, args);
        };
        var callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) func.apply(context, args);
    };
}

// Optimized scroll handler
var optimizedScroll = debounce(function() {
    // Scroll-based animations and effects
    var scrollTop = $(window).scrollTop();
    
    // Parallax effect for hero section
    $('.hero-slider').css('transform', 'translateY(' + scrollTop * 0.5 + 'px)');
    
    // Update navigation active state based on scroll position
    var sections = $('section[id]');
    sections.each(function() {
        var section = $(this);
        var sectionTop = section.offset().top - 150;
        var sectionBottom = sectionTop + section.outerHeight();
        
        if (scrollTop >= sectionTop && scrollTop < sectionBottom) {
            var sectionId = section.attr('id');
            $('.navbar-nav .nav-link').removeClass('active');
            $('.navbar-nav .nav-link[href="#' + sectionId + '"]').addClass('active');
        }
    });
}, 10);

$(window).scroll(optimizedScroll);
