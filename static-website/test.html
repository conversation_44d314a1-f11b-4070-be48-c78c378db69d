<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>网站测试页面 - YK-EcoPack</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            padding: 20px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #c9593f;
            text-align: center;
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-section h2 {
            color: #333;
            margin-bottom: 15px;
        }
        .test-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            margin: 5px 0;
            background: #f9f9f9;
            border-radius: 3px;
        }
        .status {
            padding: 5px 10px;
            border-radius: 3px;
            color: white;
            font-weight: bold;
        }
        .status.pass {
            background: #28a745;
        }
        .status.fail {
            background: #dc3545;
        }
        .status.pending {
            background: #ffc107;
            color: #333;
        }
        .nav-links {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
        }
        .nav-links a {
            padding: 10px 15px;
            background: #c9593f;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        .nav-links a:hover {
            background: #a84832;
        }
        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>YK-EcoPack 网站测试页面</h1>
        
        <div class="info-box">
            <strong>说明：</strong>这是一个简单的测试页面，用于检查网站的基本功能和资源加载情况。请在部署后运行此测试。
        </div>

        <div class="test-section">
            <h2>页面链接测试</h2>
            <div class="nav-links">
                <a href="index.html" target="_blank">主页</a>
                <a href="about.html" target="_blank">公司信息</a>
                <a href="contact.html" target="_blank">联系我们</a>
                <a href="kitchen-garbage-bags.html" target="_blank">厨余垃圾袋</a>
                <a href="pet-waste-bags.html" target="_blank">宠物垃圾袋</a>
                <a href="grocery-bags.html" target="_blank">购物袋</a>
            </div>
        </div>

        <div class="test-section">
            <h2>资源加载测试</h2>
            <div class="test-item">
                <span>jQuery 库</span>
                <span class="status pending" id="jquery-status">检测中...</span>
            </div>
            <div class="test-item">
                <span>Bootstrap CSS</span>
                <span class="status pending" id="bootstrap-css-status">检测中...</span>
            </div>
            <div class="test-item">
                <span>Bootstrap JS</span>
                <span class="status pending" id="bootstrap-js-status">检测中...</span>
            </div>
            <div class="test-item">
                <span>Font Awesome</span>
                <span class="status pending" id="fontawesome-status">检测中...</span>
            </div>
            <div class="test-item">
                <span>Owl Carousel</span>
                <span class="status pending" id="owl-status">检测中...</span>
            </div>
            <div class="test-item">
                <span>自定义样式</span>
                <span class="status pending" id="custom-css-status">检测中...</span>
            </div>
        </div>

        <div class="test-section">
            <h2>图片资源测试</h2>
            <div class="test-item">
                <span>公司Logo</span>
                <span class="status pending" id="logo-status">检测中...</span>
            </div>
            <div class="test-item">
                <span>轮播图片</span>
                <span class="status pending" id="slider-status">检测中...</span>
            </div>
            <div class="test-item">
                <span>产品图片</span>
                <span class="status pending" id="product-status">检测中...</span>
            </div>
        </div>

        <div class="test-section">
            <h2>响应式测试</h2>
            <div class="test-item">
                <span>当前屏幕宽度</span>
                <span id="screen-width">-</span>
            </div>
            <div class="test-item">
                <span>设备类型</span>
                <span id="device-type">-</span>
            </div>
        </div>

        <div class="warning-box">
            <strong>注意：</strong>
            <ul>
                <li>请确保已按照 DEPLOYMENT.md 中的说明下载所有第三方库文件</li>
                <li>请确保已复制所有必需的图片资源到正确位置</li>
                <li>如果某些测试失败，请检查文件路径和网络连接</li>
                <li>建议在不同设备和浏览器上进行测试</li>
            </ul>
        </div>
    </div>

    <script>
        // 测试资源加载
        function testResource(url, statusId, type = 'script') {
            const element = type === 'script' ? document.createElement('script') : document.createElement('link');
            const statusElement = document.getElementById(statusId);
            
            element.onload = function() {
                statusElement.textContent = '通过';
                statusElement.className = 'status pass';
            };
            
            element.onerror = function() {
                statusElement.textContent = '失败';
                statusElement.className = 'status fail';
            };
            
            if (type === 'script') {
                element.src = url;
            } else {
                element.rel = 'stylesheet';
                element.href = url;
            }
            
            document.head.appendChild(element);
        }

        // 测试图片资源
        function testImage(url, statusId) {
            const img = new Image();
            const statusElement = document.getElementById(statusId);
            
            img.onload = function() {
                statusElement.textContent = '通过';
                statusElement.className = 'status pass';
            };
            
            img.onerror = function() {
                statusElement.textContent = '失败';
                statusElement.className = 'status fail';
            };
            
            img.src = url;
        }

        // 检测设备类型
        function detectDevice() {
            const width = window.innerWidth;
            document.getElementById('screen-width').textContent = width + 'px';
            
            let deviceType = '';
            if (width < 576) {
                deviceType = '手机 (< 576px)';
            } else if (width < 768) {
                deviceType = '大屏手机 (576px - 768px)';
            } else if (width < 992) {
                deviceType = '平板 (768px - 992px)';
            } else if (width < 1200) {
                deviceType = '小屏桌面 (992px - 1200px)';
            } else {
                deviceType = '大屏桌面 (≥ 1200px)';
            }
            
            document.getElementById('device-type').textContent = deviceType;
        }

        // 运行测试
        window.onload = function() {
            // 测试JavaScript库
            testResource('assets/js/jquery.min.js', 'jquery-status');
            testResource('assets/js/bootstrap.bundle.min.js', 'bootstrap-js-status');
            testResource('assets/js/owl.carousel.min.js', 'owl-status');
            
            // 测试CSS文件
            testResource('assets/css/bootstrap.min.css', 'bootstrap-css-status', 'link');
            testResource('assets/css/font-awesome.min.css', 'fontawesome-status', 'link');
            testResource('assets/css/style.css', 'custom-css-status', 'link');
            
            // 测试图片
            testImage('assets/images/logo.png', 'logo-status');
            testImage('assets/images/slider/grocery-bags.jpg', 'slider-status');
            testImage('assets/images/products/grocery-bags-small.jpg', 'product-status');
            
            // 检测设备
            detectDevice();
        };

        // 监听窗口大小变化
        window.onresize = detectDevice;

        // 添加一些额外的检测
        setTimeout(function() {
            // 检查jQuery是否加载
            if (typeof jQuery !== 'undefined') {
                document.getElementById('jquery-status').textContent = '通过';
                document.getElementById('jquery-status').className = 'status pass';
            }
            
            // 检查Bootstrap是否加载
            if (typeof bootstrap !== 'undefined') {
                document.getElementById('bootstrap-js-status').textContent = '通过';
                document.getElementById('bootstrap-js-status').className = 'status pass';
            }
        }, 2000);
    </script>
</body>
</html>
