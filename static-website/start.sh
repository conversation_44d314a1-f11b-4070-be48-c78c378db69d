#!/bin/bash

# YK-EcoPack 网站启动脚本
# 使用方法: ./start.sh [选项]

echo "🌟 YK-EcoPack 静态网站启动脚本"
echo "=================================="

# 获取当前目录的绝对路径
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 检查是否在正确的目录
if [ ! -f "$SCRIPT_DIR/index.html" ]; then
    echo "❌ 错误: 未找到 index.html 文件"
    echo "请确保在 static-website 目录中运行此脚本"
    exit 1
fi

# 显示菜单
show_menu() {
    echo ""
    echo "请选择要执行的操作:"
    echo "1) 🏠 打开主页 (index.html)"
    echo "2) 🏢 打开公司信息页 (about.html)"
    echo "3) 📞 打开联系我们页 (contact.html)"
    echo "4) 📦 打开产品页面菜单"
    echo "5) 🔧 运行安装验证 (verify-setup.html)"
    echo "6) 🧪 运行功能测试 (test.html)"
    echo "7) 🚀 启动本地服务器"
    echo "8) 📊 查看部署状态"
    echo "9) 📖 查看帮助文档"
    echo "0) 退出"
    echo ""
}

# 产品页面菜单
show_product_menu() {
    echo ""
    echo "产品页面:"
    echo "1) 🗑️  厨余垃圾袋 (kitchen-garbage-bags.html)"
    echo "2) 🐕 宠物垃圾袋 (pet-waste-bags.html)"
    echo "3) 🛍️  购物袋 (grocery-bags.html)"
    echo "0) 返回主菜单"
    echo ""
}

# 打开文件函数
open_file() {
    local file="$1"
    local url="file://$SCRIPT_DIR/$file"
    
    echo "🌐 正在打开: $file"
    
    # 检查文件是否存在
    if [ ! -f "$SCRIPT_DIR/$file" ]; then
        echo "❌ 错误: 文件 $file 不存在"
        return 1
    fi
    
    # 根据操作系统选择打开方式
    if command -v xdg-open > /dev/null; then
        xdg-open "$url"
    elif command -v open > /dev/null; then
        open "$url"
    elif command -v start > /dev/null; then
        start "$url"
    else
        echo "📋 请手动在浏览器中打开: $url"
    fi
}

# 启动本地服务器
start_server() {
    echo "🚀 启动本地服务器..."
    
    # 检查可用的服务器
    if command -v python3 > /dev/null; then
        echo "使用 Python 3 启动服务器 (端口 8000)"
        echo "访问地址: http://localhost:8000"
        echo "按 Ctrl+C 停止服务器"
        echo ""
        cd "$SCRIPT_DIR"
        python3 -m http.server 8000
    elif command -v python > /dev/null; then
        echo "使用 Python 2 启动服务器 (端口 8000)"
        echo "访问地址: http://localhost:8000"
        echo "按 Ctrl+C 停止服务器"
        echo ""
        cd "$SCRIPT_DIR"
        python -m SimpleHTTPServer 8000
    elif command -v php > /dev/null; then
        echo "使用 PHP 启动服务器 (端口 8000)"
        echo "访问地址: http://localhost:8000"
        echo "按 Ctrl+C 停止服务器"
        echo ""
        cd "$SCRIPT_DIR"
        php -S localhost:8000
    else
        echo "❌ 未找到可用的服务器程序"
        echo "请安装 Python 或 PHP 来启动本地服务器"
        echo "或者直接在浏览器中打开 HTML 文件"
    fi
}

# 查看部署状态
show_status() {
    echo "📊 部署状态检查..."
    echo ""
    
    # 检查关键文件
    local files=("index.html" "about.html" "contact.html" "assets/css/style.css" "assets/js/main.js")
    local missing=0
    
    for file in "${files[@]}"; do
        if [ -f "$SCRIPT_DIR/$file" ]; then
            echo "✅ $file"
        else
            echo "❌ $file (缺失)"
            ((missing++))
        fi
    done
    
    echo ""
    if [ $missing -eq 0 ]; then
        echo "🎉 所有关键文件都存在！"
    else
        echo "⚠️  发现 $missing 个文件缺失"
    fi
    
    # 统计文件数量
    local html_count=$(find "$SCRIPT_DIR" -name "*.html" | wc -l)
    local css_count=$(find "$SCRIPT_DIR/assets/css" -name "*.css" 2>/dev/null | wc -l)
    local js_count=$(find "$SCRIPT_DIR/assets/js" -name "*.js" 2>/dev/null | wc -l)
    local img_count=$(find "$SCRIPT_DIR/assets/images" -type f 2>/dev/null | wc -l)
    
    echo ""
    echo "📈 文件统计:"
    echo "   HTML 文件: $html_count 个"
    echo "   CSS 文件: $css_count 个"
    echo "   JS 文件: $js_count 个"
    echo "   图片文件: $img_count 个"
}

# 显示帮助
show_help() {
    echo "📖 YK-EcoPack 网站帮助"
    echo "======================"
    echo ""
    echo "文件说明:"
    echo "  index.html              - 网站主页"
    echo "  about.html              - 公司信息页"
    echo "  contact.html            - 联系我们页"
    echo "  kitchen-garbage-bags.html - 厨余垃圾袋产品页"
    echo "  pet-waste-bags.html     - 宠物垃圾袋产品页"
    echo "  grocery-bags.html       - 购物袋产品页"
    echo "  verify-setup.html       - 安装验证页面"
    echo "  test.html               - 功能测试页面"
    echo ""
    echo "目录结构:"
    echo "  assets/css/             - 样式文件"
    echo "  assets/js/              - JavaScript文件"
    echo "  assets/images/          - 图片资源"
    echo ""
    echo "文档文件:"
    echo "  README.md               - 项目说明"
    echo "  DEPLOYMENT.md           - 部署指南"
    echo "  DEPLOYMENT_STATUS.md    - 部署状态报告"
    echo "  PROJECT_SUMMARY.md      - 项目总结"
    echo ""
    echo "联系信息:"
    echo "  邮箱: <EMAIL>"
    echo "  电话: +86 150 1527 1728"
}

# 主循环
main() {
    while true; do
        show_menu
        read -p "请输入选项 (0-9): " choice
        
        case $choice in
            1)
                open_file "index.html"
                ;;
            2)
                open_file "about.html"
                ;;
            3)
                open_file "contact.html"
                ;;
            4)
                while true; do
                    show_product_menu
                    read -p "请输入选项 (0-3): " product_choice
                    
                    case $product_choice in
                        1)
                            open_file "kitchen-garbage-bags.html"
                            ;;
                        2)
                            open_file "pet-waste-bags.html"
                            ;;
                        3)
                            open_file "grocery-bags.html"
                            ;;
                        0)
                            break
                            ;;
                        *)
                            echo "❌ 无效选项，请重新选择"
                            ;;
                    esac
                done
                ;;
            5)
                open_file "verify-setup.html"
                ;;
            6)
                open_file "test.html"
                ;;
            7)
                start_server
                ;;
            8)
                show_status
                ;;
            9)
                show_help
                ;;
            0)
                echo "👋 再见！"
                exit 0
                ;;
            *)
                echo "❌ 无效选项，请重新选择"
                ;;
        esac
        
        echo ""
        read -p "按回车键继续..."
    done
}

# 如果有命令行参数，直接执行对应操作
if [ $# -gt 0 ]; then
    case $1 in
        "index"|"home")
            open_file "index.html"
            ;;
        "about")
            open_file "about.html"
            ;;
        "contact")
            open_file "contact.html"
            ;;
        "verify")
            open_file "verify-setup.html"
            ;;
        "test")
            open_file "test.html"
            ;;
        "server")
            start_server
            ;;
        "status")
            show_status
            ;;
        "help")
            show_help
            ;;
        *)
            echo "❌ 未知参数: $1"
            echo "可用参数: index, about, contact, verify, test, server, status, help"
            exit 1
            ;;
    esac
else
    main
fi
