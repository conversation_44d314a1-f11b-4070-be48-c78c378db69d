<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Font Awesome 测试</title>
    <link rel="stylesheet" href="assets/css/font-awesome.min.css">
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #c9593f;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-test {
            display: flex;
            align-items: center;
            margin: 15px 0;
            padding: 10px;
            background: #f9f9f9;
            border-radius: 5px;
        }
        .icon-test i {
            margin-right: 15px;
            font-size: 20px;
            color: #c9593f;
            width: 30px;
            text-align: center;
        }
        .status {
            margin-top: 20px;
            padding: 15px;
            border-radius: 5px;
            text-align: center;
            font-weight: bold;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1><i class="fa fa-check-circle"></i> Font Awesome 图标测试</h1>
        
        <div class="icon-test">
            <i class="fa fa-home"></i>
            <span>主页图标 (fa-home)</span>
        </div>
        
        <div class="icon-test">
            <i class="fa fa-phone"></i>
            <span>电话图标 (fa-phone)</span>
        </div>
        
        <div class="icon-test">
            <i class="fa fa-envelope"></i>
            <span>邮箱图标 (fa-envelope)</span>
        </div>
        
        <div class="icon-test">
            <i class="fa fa-map-marker"></i>
            <span>地址图标 (fa-map-marker)</span>
        </div>
        
        <div class="icon-test">
            <i class="fa fa-facebook"></i>
            <span>Facebook图标 (fa-facebook)</span>
        </div>
        
        <div class="icon-test">
            <i class="fa fa-twitter"></i>
            <span>Twitter图标 (fa-twitter)</span>
        </div>
        
        <div class="icon-test">
            <i class="fa fa-instagram"></i>
            <span>Instagram图标 (fa-instagram)</span>
        </div>
        
        <div class="icon-test">
            <i class="fa fa-youtube-play"></i>
            <span>YouTube图标 (fa-youtube-play)</span>
        </div>
        
        <div class="icon-test">
            <i class="fa fa-chevron-up"></i>
            <span>向上箭头 (fa-chevron-up)</span>
        </div>
        
        <div class="icon-test">
            <i class="fa fa-long-arrow-right"></i>
            <span>长箭头 (fa-long-arrow-right)</span>
        </div>
        
        <div id="status" class="status">
            <span id="status-text">检测中...</span>
        </div>
        
        <div style="text-align: center; margin-top: 20px;">
            <a href="index.html" style="color: #c9593f; text-decoration: none;">
                <i class="fa fa-arrow-left"></i> 返回主页
            </a>
        </div>
    </div>

    <script>
        // 检测Font Awesome是否正确加载
        window.onload = function() {
            setTimeout(function() {
                // 检查是否有Font Awesome样式被应用
                var testElement = document.createElement('i');
                testElement.className = 'fa fa-home';
                testElement.style.position = 'absolute';
                testElement.style.left = '-9999px';
                document.body.appendChild(testElement);
                
                var computedStyle = window.getComputedStyle(testElement, ':before');
                var content = computedStyle.getPropertyValue('content');
                
                document.body.removeChild(testElement);
                
                var statusElement = document.getElementById('status');
                var statusText = document.getElementById('status-text');
                
                if (content && content !== 'none' && content !== '') {
                    statusElement.className = 'status success';
                    statusText.textContent = '✅ Font Awesome 图标加载成功！所有图标都应该正常显示。';
                } else {
                    statusElement.className = 'status error';
                    statusText.textContent = '❌ Font Awesome 图标加载失败。请检查CSS和字体文件是否正确。';
                }
            }, 1000);
        };
    </script>
</body>
</html>
