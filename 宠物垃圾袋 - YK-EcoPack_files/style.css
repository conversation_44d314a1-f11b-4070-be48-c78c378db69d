/*
Theme Name:  Cctv Security Pro
Theme URI: https://luzukdemo.com/demo/cctv-security-pro/
Author: luzuk Themes
Author URI: https://www.luzuk.com/
Description:  
Version: 4.0
License: GNU General Public License v2 or later
License URI: http://www.gnu.org/licenses/gpl-2.0.html
Text Domain: cctv-security-pro
Tags: two-columns, right-sidebar, left-sidebar, flexible-header, custom-background, custom-colors, custom-header, custom-menu, custom-logo, featured-image-header, featured-images, footer-widgets, post-formats, sticky-post, theme-options, threaded-comments, translation-ready, blog, entertainment, news, portfolio
*/
html {
    font-family: sans-serif;
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust:     100%;
}
body {
    margin: 0;
}
article,
aside,
details,
figcaption,
figure,
footer,
header,
main,
menu,
nav,
section,
summary {
    display: block;
    margin: 0;
}
audio,
canvas,
progress,
video {
    display: inline-block;
    vertical-align: baseline;
}
audio:not([controls]) {
    display: none;
    height: 0;
}
[hidden],
template {
    display: none;
}
a {
    text-decoration: :none;
}
a:active,
a:hover {
    outline: 0;
    text-decoration:none;
}

a { 
    word-break: break-word;
    outline: 0 none;
    text-decoration: none;
}
abbr[title] {
    border-bottom: 1px dotted;
}
b,
strong {
    font-weight: bold;
}
dfn {
    font-style: italic;
}
mark {
    background: #ff0;
    color: #000;
}
small {
    font-size: 80%;
}
sub,
sup {
    font-size: 75%;
    line-height: 0;
    position: relative;
    vertical-align: baseline;
}
sup {
    top: -0.5em;
}
sub {
    bottom: -0.25em;
}
img {
    border: 0;
}
svg:not(:root) {
    overflow: hidden;
}
hr {
    box-sizing: content-box;
    height: 0;
}
pre {
    overflow: auto;
}
code,
kbd,
pre,
samp {
    font-family: monospace, monospace;
    font-size: 1em;
}
button,
input,
optgroup,
select,
textarea {
    color: inherit;
    font: inherit;
    margin: 0;
}
button {
    overflow: visible;
}
button,
select {
    text-transform: none;
}
button,
html input[type="button"],
input[type="reset"],
input[type="submit"] {
    -webkit-appearance: button;
    cursor: pointer;
}
button[disabled],
html input[disabled] {
    cursor: default;
}
button::-moz-focus-inner,
input::-moz-focus-inner {
    border: 0;
    padding: 0;
}
input {
    line-height: normal;
}
input[type="checkbox"],
input[type="radio"] {
    box-sizing: border-box;
    padding: 0;
}
input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
    height: auto;
}
input[type="search"] {
    -webkit-appearance: textfield;
}
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-decoration {
    -webkit-appearance: none;
}
fieldset {
    margin: 0 2px;
    padding-top: 15px;
}
legend {
    border: 0;
    padding: 0;
}
textarea {
    overflow: auto;
}
optgroup {
    font-weight: bold;
}
table {
    border-collapse: collapse;
    border-spacing: 0;
}
td,
th {
    padding:0px;
}
/*--------------------------------------------------------------
# Typography
--------------------------------------------------------------*/
body,
button,
input,
select,
textarea {
    color: #444;
    font-size: 16px;
    line-height: 1.2;
    font-weight: 400;
}
h1,
h2,
h3,
h4,
h5,
h6 {
    margin: 0;
    font-weight: 600;
    line-height: 1.2;
    font-family: 'Oswald', sans-serif;
}
h1{
    font-size: 28px;
    margin-bottom: 15px;
}
h2{
    font-size: 26px;
    margin-bottom: 15px;
}
h3{
    font-size: 30px;
    margin:20px 0 15px 0;
}
h4{
    font-size: 22px;
    margin-bottom: 10px;
}
h5{
    font-size: 20px;
    margin-bottom: 10px;
}
h6{
    font-size: 18px;
    margin-bottom: 10px;
}
p { 
    word-break: break-word;
    margin:0 0 15px;
}
dfn,
cite,
em,
i {
    font-style: italic;
}
blockquote {
    margin: 0 0px 15px;
    padding: 21px 30px 18px;
    background: #f2f2f2;
    font-size: 16px;
    color: #212121;
    font-style: italic;
    font-weight: 400;
}
.wp-block-quote:not(.is-large):not(.is-style-large) {
    padding-left: 1.7em !important;
}
#innerpage-box blockquote p{
    margin: 0;
    font-weight: 400;
}
.wp-block-image img {
    max-width: 100%;
    /*width: 100%;*/
}
pre {
    background: #eee;
    font-family: "Courier 10 Pitch", Courier, monospace;
    font-size: 15px;
    line-height: 1.6;
    margin-bottom: 15px;
    max-width: 100%;
    overflow: auto;
    padding: 15px;
}
code,
kbd,
tt,
var {
    font-family: Monaco, Consolas, "Andale Mono", "DejaVu Sans Mono", monospace;
    font-size: 15px;
}
abbr,
acronym {
    border-bottom: 1px dotted #666;
    cursor: help;
}
mark,
ins {
    background: #fff9c0;
    text-decoration: none;
}
big {
    font-size: 125%;
}
select {
    border: 1px solid #EEE;
    height: 40px;
    padding: 3px 40px 3px 8px;
    background-color: transparent;
    line-height: 100%;
    outline: 0;
    background-image: url(images/arrow.png);
    background-position: right;
    center: ;
    background-repeat: no-repeat;
    position: relative;
    cursor: pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
}
/*--------------------------------------------------------------
# Elements
--------------------------------------------------------------*/

html {
    box-sizing: border-box;
}
*,
*:before,
*:after { /* Inherit box-sizing to make it easier to change the property for components that leverage other behavior; see http://css-tricks.com/inheriting-box-sizing-probably-slightly-better-best-practice/ */
    box-sizing: inherit;
}
blockquote:before,
blockquote:after,
q:before,
q:after {
    content: "";
}
blockquote,
q {
    quotes: "" "";
        -moz-box-shadow: -1px 0px 9px rgba(152, 152, 131, 0.2);
    -webkit-box-shadow: -1px 0px 9px rgba(152, 152, 131, 0.2);
    box-shadow: -1px 0px 9px rgba(152, 152, 131, 0.2);
}
hr {
    background-color: #ccc;
    border: 0;
    height: 1px;
    margin-bottom: 15px;
}
ul,
ol {
    margin: 0 0 15px 20px;
    padding: 0;
}
ul {
    list-style: disc;
}
ol {
    list-style: decimal;
}
li > ul,
li > ol {
    margin-bottom: 0;
    margin-left: 15px;
}
dt {
    font-weight: bold;
}
dd {
    margin: 0 15px 15px;
}
img {
    height: auto; /* Make sure images are scaled correctly. */
    max-width: 100%; /* Adhere to container width. */
    vertical-align: middle;
}
table {
    margin: 0 0 15px;
    width: 100%;
}
/*--------------------------------------------------------------
# Forms
--------------------------------------------------------------*/
button,
input[type="button"],
input[type="reset"],
input[type="submit"] {
    background: #fe5722;
    padding: 10px 20px;   
    color: #FFF;
    border: 0;
    -moz-transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border-radius: 5px;
    /*box-shadow: 0px 6px 10px -4px rgba(0,0,0,0.4);*/
        font-size: 16px;
}
button:hover,
input[type="button"]:hover,
input[type="reset"]:hover,
input[type="submit"]:hover {
    /*background: #2d2d2d;*/
    opacity: 1;
}
button:focus,
input[type="button"]:focus,
input[type="reset"]:focus,
input[type="submit"]:focus,
button:active,
input[type="button"]:active,
input[type="reset"]:active,
input[type="submit"]:active {
    outline: 0;
}
input[type="text"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="search"],
input[type="number"],
input[type="tel"],
input[type="range"],
input[type="date"],
input[type="month"],
input[type="week"],
input[type="time"],
input[type="datetime"],
input[type="datetime-local"],
input[type="color"],
textarea {
    color: #404040;
    border: 1px solid #EEE;
    transition:all 0.3s ease-in-out;
    -moz-transition:all 0.3s ease-in-out;
    -webkit-transition:all 0.3s ease-in-out;
    vertical-align: top;
}

.widget.widget_categories select, {
    border: 1px solid #EEE;
    height: 40px;
    padding: 3px 40px 3px 8px;
    line-height: 100%;
    outline: 0;
    background-image: url("images/arrow.png");
    background-position: right; center;
    background-repeat: no-repeat;
    position: relative;
    cursor: pointer;
    -webkit-appearance: none;
    -moz-appearance: none;
}
input[type="text"]:focus,
input[type="email"]:focus,
input[type="url"]:focus,
input[type="password"]:focus,
input[type="search"]:focus,
input[type="number"]:focus,
input[type="tel"]:focus,
input[type="range"]:focus,
input[type="date"]:focus,
input[type="month"]:focus,
input[type="week"]:focus,
input[type="time"]:focus,
input[type="datetime"]:focus,
input[type="datetime-local"]:focus,
input[type="color"]:focus,
textarea:focus {
    outline: 0;
}
input[type="text"],
input[type="email"],
input[type="url"],
input[type="password"],
input[type="search"],
input[type="number"],
input[type="tel"],
input[type="range"],
input[type="date"],
input[type="month"],
input[type="week"],
input[type="time"],
input[type="datetime"],
input[type="datetime-local"],
input[type="color"] {
    padding: 10px 8px;
    width: 100%;
}
textarea {
    padding: 8px;
    width: 100%;
}
/*--------------------------------------------------------------
## Links
--------------------------------------------------------------*/
a {
    word-break: break-word;
    color: #f9c12c;
    text-decoration: none;
    transition: all 0.3s ease; 
    -moz-transition: all 0.3s ease; 
    -webkit-transition: all 0.3s ease; 
}
a:focus {
    outline: 0;
}
a:hover,
a:active {
    outline: 0;
}
/*--------------------------------------------------------------
# Accessibility
--------------------------------------------------------------*/
/* Text meant only for screen readers. */
.screen-reader-text {
    clip: rect(1px, 1px, 1px, 1px);
    position: absolute !important;
    height: 1px;
    width: 1px;
    overflow: hidden;
}
.screen-reader-text:focus {
    background-color: #f1f1f1;
    border-radius: 3px;
    box-shadow: 0 0 2px 2px rgba(0, 0, 0, 0.6);
    clip: auto !important;
    color: #21759b;
    display: block;
    font-size: 14px;
    font-size: 0.875rem;
    font-weight: bold;
    height: auto;
    left: 5px;
    line-height: normal;
    padding: 15px 23px 14px;
    text-decoration: none;
    top: 5px;
    width: auto;
    z-index: 100000; /* Above WP toolbar. */
}
/*--------------------------------------------------------------
# Alignments
--------------------------------------------------------------*/
.alignleft {
    display: inline;
    float: left;
    margin-right: 15px;
    border-radius: 10px;
}
.alignright {
    display: inline;
    float: right;
    margin-left: 15px;
    border-radius: 10px;
}
.aligncenter {
    clear: both;
    display: block;
    margin-left: auto;
    margin-right: auto;
    border-radius: 10px;
}
/*--------------------------------------------------------------
# Widgets
--------------------------------------------------------------*/
.widget {
    margin: 0 0 15px;
}
/* Make sure select elements fit in widgets. */
.widget select {
    max-width: 100%;
    width: 100%;
}
/* Search widget. */
.widget_search .search-submit {
    display: none;
}
/*--------------------------------------------------------------
## Asides
--------------------------------------------------------------*/
.widget-area .widget-title{
    padding: 5px;
    margin-bottom: 20px;
    font-size: 18px;
    color: #6d6d6d;
    margin: 0;
    position: relative;
    display: block;
    text-transform: uppercase;
    margin: 0 0 12px;
    background: #c9593f;
    color: #fff;
    border-radius: 20px 0 20px;
    text-align: center;
    font-weight: 600;
}

.widget-area ul{
    list-style: none;
    padding: 0;
    margin: 0;
    border: 1px solid #eaeaea;
}
.widget-area .social-profile-icons ul li {
    display: inline-block;
}
.widget-area .social-profile-icons ul li:before {
    display: none;
}

.widget-area ul ul{
    margin-top: 5px;
}
.widget-area ul ul li{
    padding-left: 10px;
}
.widget-area ul ul li:last-child{
    border-bottom: 0;
    padding-bottom: 0;
}
.widget-area li{
    padding: 5px 0;
    line-height: normal;
    margin: 0;
    padding: 0;
    position: relative;
    box-shadow: unset;
    border-radius: 0;
    border-bottom: 1px solid #eaeaea;
    padding-left: 15px;
}
.widget-area li.recentcomments {
    padding: 10px 15px;
}
.widget-area a{
    text-decoration: none;
    color: #404040;
    display: inline-block;
    padding: 14px 0;
    font-weight: 400;
    line-height: 100%;
    text-transform: capitalize;
    margin: 0;
}
.recentcomments a {
    display: inline !important;
    padding: 0 !important;
    margin: 0 !important;
}
.widget-area a:hover{
    color: #fe5722
}
.widget-area .widget{
    margin-bottom: 15px;
    padding: 2em 1em;
    background: #f1f1f1;
}
/*--------------------------------------------------------------
## Sidebar Area
--------------------------------------------------------------*/
div#secondary input[type="submit"] {
    width: 100%;
}
#secondary input[type="text"]{
    width: 100%;
    margin-bottom: 10px;
    font-size: 15px;
    background: transparent;
    border-radius: 5px;
}

#secondary input[type="text"],
#secondary input[type="email"],
#secondary input[type="url"],
#secondary input[type="password"],
#secondary input[type="search"],
#secondary input[type="number"],
#secondary input[type="tel"],
#secondary input[type="range"],
#secondary input[type="date"], 
#secondary input[type="month"], 
#secondary input[type="week"], 
#secondary input[type="time"], 
#secondary input[type="datetime"], 
#secondary input[type="datetime-local"], 
#secondary input[type="color"], 
#secondary input[type="file"],
#secondary textarea, 
#secondary select {
    width: 100%;
    margin-bottom: 10px;
    font-size: 15px;
    border-radius: 5px;
    padding: 10px;
    line-height: 1.6;
        background-color: transparent;
}
#secondary textarea {
    height: 90px;
}
#secondary .gallery-columns-3 .gallery-item {
    max-width: 33.33%;
}
#secondary figure.gallery-item a img:hover, #secondary .widget img:hover{
    opacity: 0.7;
}
#secondary figure.gallery-item a img, #secondary .widget img{
    padding: 5px;
}
/*--------------------------------------------------------------
## Comments
--------------------------------------------------------------*/
.comment-content a {
    word-wrap: break-word;
}
.bypostauthor {
    display: block;
}
.author-email-url{
    margin-left: -2%;
}

#commentsAdd input[type="submit"]{
    background: #fc226a;
    padding: 10px 20px;
    color: #FFF;
    border: 0;
    -moz-transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    height: 50px;
    font-size: 16px;
}
#commentsAdd input[type="submit"]:hover{
    border-radius: 5px;
    opacity: 1;
}
.comment-form-author,
.comment-form-email,
.comment-form-url{
    float: left;
    margin-left: 2%;
    width: 31.33333%;
}
.author-email-url input{
    width: 100%;
}
.comment-form .form-submit{
    margin-bottom: 0
}
h3#reply-title,
h3.comments-title{
    position: relative;
    padding-bottom: 15px;
    margin-bottom: 30px;
}
h3#reply-title:after,
h3.comments-title:after{
    content: "";
    position: absolute;
    left: 0;
    top: 100%;
    width: 30px;
    background: #fe5722;
    height: 2px;
}
.logged-in-as,
.comment-notes{
    font-size: 0.9em;
}
#comments ul{
    list-style: none;
    margin: 0;
    padding: 0;
}
#comments li.comment{
    margin: 0 0 20px;
}
#comments li.comment .children{
    display: block;
    margin: 20px 0 0 30px;
    padding: 0px;
    list-style: none;
}
article.comment-body {
    background: #fff;
    margin: 0px;
    position: relative;
}
.comment-list a{
    color: #404040;
}
.comment-list a:hover{
    color: #fe5722 !important;
}
.comment-list .comment-respond{
    margin-top: 20px;
    background: #FFF;
    padding: 20px;
}
.comment-list .vcard img {
    border-radius: 50%;
    margin-right: 10px;
}
.comment-list .vcard .fn{
    font-weight: normal;
    font-size: 16px;
}
.comment-list .edit-link{
    position: absolute;
    right: 0;
    top: 0;
}
.comment-list .edit-link a{
    font-size: 14px;
    display: inline-block;
    padding: 10px;
}
.comment-list .comment-meta {
    margin-bottom: 15px;
} 
.comment-list .comment-metadata {
    border-top: 1px solid #EEE;
    padding-top: 10px;
    font-size: 14px;
}
.comment-list .comment-metadata a {
    float: left;
}
.comment-list .comment-metadata .reply {
    float: right;
}
#cancel-comment-reply-link {
    float: right;
}
.nav-previous a,
.nav-next a{
    background: #fe5722;
    color: #FFF;
    display: inline-block;
    padding: 0 10px;
    font-size: 12px;
    line-height: 22px;
    position: relative;
}
.nav-next a{
    margin-right: 13px;
}
.nav-next a:after{
    content: "";
    position: absolute;
    border-left: 11px solid #fe5722;
    border-top: 11px solid transparent;
    border-bottom: 11px solid transparent;
    top: 0;
    right: -11px;
}
.nav-previous a{
    margin-left: 11px;
}
.nav-previous a:after{
    content: "";
    position: absolute;
    border-right: 11px solid #fe5722;
    border-top: 11px solid transparent;
    border-bottom: 11px solid transparent;
    top: 0;
    left: -11px;
}
.no-comments{
    color: #fe5722;
}
.pagination{
    text-align: center;
}
.pagination .page-numbers{
    display: inline-block;
    margin: 0 2px;
    background: #fe5722;
    color: #FFF !important;
    padding: 8px 12px;
    line-height: 1;
    border-radius: 2px;
}
.pagination .page-numbers.current,
.pagination .page-numbers:hover{
    background: #333;
    color: #FFF;
}
/*--------------------------------------------------------------
# Media
--------------------------------------------------------------*/

.page-content .wp-smiley,
.entry-content .wp-smiley,
.comment-content .wp-smiley {
    border: none;
    margin-bottom: 0;
    margin-top: 0;
    padding: 0;
}
/* Make sure embeds and iframes fit their containers. */
embed,
iframe,
object {
    max-width: 100%;
}
/*--------------------------------------------------------------
## Captions
--------------------------------------------------------------*/
.wp-caption {
    margin-bottom: 15px;
    max-width: 100%;
}
.wp-caption img[class*="wp-image-"] {
    display: block;
    margin: 0 auto;
}
.wp-caption-text {
    text-align: center;
}
.wp-caption .wp-caption-text {
    margin: 0.8075em 0;
}
/*--------------------------------------------------------------
## Pagination
--------------------------------------------------------------*/
.pagingation{
    margin: 0px 0 0 0;
    padding: 1em 0;
     clear: both; 
    text-align: center;

}
.pagingation a, .woocommerce nav.woocommerce-pagination ul li a{
    background:rgba(0,0,0,0.7);
    color:#fff;
    padding:10px 15px;
    border-radius: 5px;
        border: 1px solid #ccc;
    background: #fff;
    margin: 0 1px 0 0;
    display: inline-block;
    line-height: 1.2em;
    text-decoration: none;
    color: #555;
    padding: 10px 14px;
    margin-right: 8px;
    transition: all 0.25s ease;
}
.pagingation a:hover, .woocommerce nav.woocommerce-pagination ul li a:hover{
    color:#fff;
}
.pagingation .current{
    /*background: #fe5722;*/
    color:#fff;
    padding:10px 15px;
    border-radius: 5px;
    color: #fff;
    margin: 0 1px 0 0;
    display: inline-block;
    line-height: 1.2em;
    text-decoration: none;
    padding: 10px 14px;
    border: 1px solid #ccc;
    margin-right: 8px;
}

.page-content #searchform input[type="submit"] {
    margin-top: 15px;
}
.woocommerce-error, 
.woocommerce-info, 
.woocommerce-message {
    padding: 1em 0em 1em 3.5em !important;}
/*--------------------------------------------------------------
## Galleries
--------------------------------------------------------------*/
.gallery {
    margin-bottom: 15px;
}
.gallery-item {
    display: inline-block;
    text-align: center;
    vertical-align: top;
    width: 100%;
}
.s-footer .gallery-item {
    width: 30%;
}
.gallery-caption {
    display: block;
}
.ht-gallery-member-image {
    position: relative;
    overflow: hidden;
}
/*--------------------------------------------------------------
## General
--------------------------------------------------------------*/

.home main#innerpage-box {
    margin-top: 8em;
}
.clearfix {
    clear: both;
}
.blink {
    text-decoration: blink;
    -webkit-animation-name: blinker;
    -webkit-animation-duration: 0.6s;
    -webkit-animation-iteration-count: infinite;
    -webkit-animation-timing-function: ease-in-out;
    -webkit-animation-direction: alternate;
}
.justify-content-center{
    -ms-flex-pack: center!important;
    -webkit-box-pack: center!important;
    justify-content: center!important;
    text-align: center;
}
.section-title .sub-title{
    margin-bottom: 1em;
    font-size: 18px;
    font-weight: 600;
    /*letter-spacing: 3px;*/
}
.section-title{
    margin-bottom: 3em;
}
.section-title h2 {
    word-break: break-word;
    font-weight: 600;
    font-size: 30px;
    margin: 3px 0;
    display: inline-block;
    position: relative;
}

.section-title h3 {
    word-break: break-word;
    font-weight: 600;
    font-size: 30px;
    margin: 3px 0;
    display: inline-block;
    position: relative;
}
.section-title .border {
    display: block;
    margin-top: 0px;
    content: " ";
    text-shadow: none;
    width: 80px;
    border-radius: 50px;
    border-style: solid;
    border-width: 6px;
    border-left: none;
    border-right: none;
    border-bottom: none;
    border-color: #3ff;
    position: relative;
}
.section-title .border1 {
    display: block;
    position: relative;
    margin: 1em auto 0;
    content: " ";
    text-shadow: none;
    width: 140px;
    border-radius: 50px;
    border-style: solid;
    border-width: 6px;
    border-left: none;
    border-right: none;
    border-bottom: none;
    border-color: #3ff;
}
.subheading{
    font-size: 18px;
    font-weight: 500;
    position: relative;
   /* line-height: 30px;*/
}
.inner-area-title {    
    font-size: 23px;
    font-weight: 500;
    word-break: break-word;
}
.section-area-text {    
    margin-bottom: 24px;
    font-size: 15px;
    line-height: 25px;
    font-weight: 500;
    word-break: break-word;
}
.titleborder:after{
    -webkit-animation: sp 3s linear infinite;
    animation: sp 3s linear infinite;
}

@keyframes sp {
    from {
        transform: translate(0, 0px);
    }

    65% {
        transform: translate(0, 10px);
    }

    to {
        transform: translate(0, -0px);
    }
}

.textcenter{
    text-align: center;
}
:hover{
    -webkit-transition-duration: 1s;   
    -moz-transition-duration: 1s;   
    -o-transition-duration: 1s;   
    transition-duration: 1s;
}
section{
}
section .darkbox{
    padding:3em 0;
    background: rgba(0,0,0,0.6)
}
section .lightbox{
    padding: 5em 0;
}
.padding0{
    padding: 0;
}
.ht-section-title-tagline{
    margin-bottom: 3em;
}
.ht-section-title{
    font-size: 33px;
    color: #323232;
    margin: 0 auto;
    font-weight: bold;
    line-height: 60px;
    word-break: break-word;
    position: relative;
}
.ht-section-title:last-child{
    margin-bottom: 0;
}
.darkbox .ht-section-tagline{
    font-size:16px;
    width: 70%;
    margin: 0 auto 0;
    color:#fff;
}
.ht-main-title{
    position: relative;
    margin: 2em 0 0.5em;
    letter-spacing: 1px;
    font-weight: 600;
    /*text-shadow: 0em 0em 0.4em rgba(255,255,255,0.6);*/
    text-align: center;
}

.archive .ht-main-title, .single-product .ht-main-title{
    margin: 0em 0 0.5em;
    padding: 0.5em 0 0;
}
.category .ht-main-title {
    margin: 0 0 1.5em;
}
.ht-main-title:last-child{
    margin-bottom: 0;
}
.ht-site-title, .site-title {
    font-weight: 600;
    font-size: 28px;
    line-height: 1;
    padding: 0;
    padding-top: 15px;
   /* text-align: center;*/
}
.ht-site-title a, .site-title a{
    text-decoration: none;
    color: #4a4a4a;
}
.ht-site-description{
    color: #ffffff;
    font-size: 14px;
    font-weight: normal;
    margin: 0;
    text-align: center;
    z-index: 2;
    position: relative;
}
#total-breadcrumbs,
.woocommerce .woocommerce-breadcrumb{
     font-size: 15px;
    margin: 0;
    color: inherit;
    display: block !important;
    text-align: left;
    padding: 0px 0 2.5em;
}
.single-productpage .innerpage-whitebox {
    margin-top: 0;
    padding: 1em;
}
.single-productpage #sidebars {
    margin-top: 20px;
}
.single-productpage .ht-main-title {
    padding: 20px;
}
.woocommerce ul.product_list_widget li img{
   margin: 0 15px 0px 0;
}
.woocommerce .woocommerce-breadcrumb a,
#total-breadcrumbs a{
    color: #000;
}
.woocommerce .woocommerce-breadcrumb a:hover,
#total-breadcrumbs a:hover{
    color: #fe5722;
}
.taxonomy-description,
.term-description{
    margin-bottom: 15px;
    padding: 0px 25px 25px 25px;
}
.taxonomy-description p:last-child,
.term-description p:last-child{
    margin-bottom: 0;
}
/*--------------------------------------------------------------
## Breadcrumb
--------------------------------------------------------------*/

.breadcrumbbox {
    padding: 0px 0 2.5em;
    position: relative;
    text-align: center;
}
.breadcrumbbox span{
    color:#000;
    margin:0 3px;
    word-break:break-word;
}
.breadcrumbbox .button {
    display: inline-block;
    background: #e9ecef30;
    padding: 10px 20px;
    border-radius: 50px;
}
/*===========================================
## Search & cart icon
======================================*/
.sitenav .search-icon{
    position: absolute;
    right: 0;
    top: 20px;
    cursor: pointer;
    padding: 0;  
}
.sitenav .search-icon i{
    margin: 0;
    font-size: 16px;
    color: #fff;
    background: #ac8e50;
    padding: 13px 15px;
    border-radius: 50%;
}
.sitenav .search-bar {
    display: none;
    position: absolute;
    bottom: 16px;
    z-index: 200;
    right: 13%;
}
.sitenav .search-bar input{
   display:inline-block;
}
.sitenav .search-bar input[type="text"]{ 
   width:100%;
   background: rgba(33, 33, 33, 0.8);
   color:#fff;
   border:none;
}
.sitenav .search-bar input[type='submit']{
    background: #000;
    color: #000;
    width: 15%;
    padding: 0;
}
.fa-search {
   &:hover {
      color: rgba(255, 255, 255, 0.75);
  }
}

/*----------------------------------------*/
/*  01. Header Area
/*----------------------------------------*/

.navigation .menu-click i{
    display: none;
}
.resp_header_logo {
    display: none;
}
.overlap-header {
    position: relative;
    top: 0px;
}
.page-main-header{
    background: #fe5722;
    color:#fff;
    clear: both;
    /*padding: 12em 0 4em;*/
    background: linear-gradient(0deg,#8972ea,#516ced 80%) no-repeat;
    position: relative;
}
.page-main-header .overlay1 {
    background: red;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    position: absolute;
    opacity: 0.3;
}

header .HeaderRbx {
    padding-right: 1em;
}
.share-btn ul{
    transition:all 0.5s;
    text-align: right;
}
.share-btn ul li{
    display:inline-block;
    transition:all 0.5s;
    text-align: center;
}
.share-btn ul li a.site-button{
    display: block;
    background: none;
    color: #f3f;
    border-radius: 0%;
    border-left: none;
    font-size: 18px;
    /*padding-top: 10px;*/
    margin:8px 6px 0 7px;
    font-weight: bold;
    position: relative;
    overflow: hidden;
    text-align: center;
}

header .Reg {
    font-size: 16px;
    font-weight: 500;
    padding: 10px 0;
}
header .Reg p{
    padding-left: 15px;
    margin-bottom: 0;
    font-size: 16px;
    font-weight: 400;
}
header .Reg a i{
    padding-right: 10px;
    font-weight: 600;
}
.header-transparent .resp_head_box {
    padding: 12px 0 15px;
}
.pd-0{
    padding: 0;
}
.pd-1{
    padding: 0 3px;
}
.logo {
    padding: 0px 5px 10px 5px;
}
.logo-header.mostion {
    text-align: center;
    z-index: 1;
    position: relative;
}

.logobox img{
    position: relative;
}
.head-menu {
    position: relative;
}
header.site-header .main-dispaly.padding0 {
    padding-right: 0;
}
header .header-text{
    font-size: 14px;
    font-weight: 600;
}
.header-dtime{
    font-size: 14px;
    font-weight: 600;
}
.header-dtime i{
    font-size: 17px;
    padding-right: 6px;
}
header.site-header li {
    display: inline-block;
    list-style-type: none;
}
ul.hd-contact {
    padding-top: 1em;
}
ul.hd-contact li i {
    color: #1d62b8;
    padding-right: 8px;
    font-size: 15px;
}
ul.hd-contact li a{
    font-size: 14px;
    color: #354bb1;
    font-weight: 600;
    word-break: break-word;
}

header .Rqst-btn a {
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-decoration: none;
    font-weight: 300;
    font-size: 16px;
    top: 2em;
    margin-right: 1px;
    padding: 10px;
    color: #fff;
    transition: all 0.5s;
    border-radius: 0px;
    cursor: pointer;
    text-align: center;
    z-index: 9;
    max-width: 12em;
   word-break: break-all;
    background-color: #000;
    border: none;
    float: left;
}
header .Rqst-btn a:after {
  position: absolute;
  content: "";
  width: 100%;
  height: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  border-radius: 0px;
   background-color: #f3f;
/*background-image: linear-gradient(315deg, #4dccc6 0%, #96e4df 74%);*/
  transition: all 0.3s ease;
}
header .Rqst-btn a:hover {
  color: #fff;
}
header .Rqst-btn a:hover:after {
  top: 0;
  height: 100%;
}
header .Rqst-btn a:active {
  top: 2px;
}
/* = Navigation hover and active menu css
-------------------------------------------------------------- */
header .header-right {
    position: relative;
}
.head-menu {
    position: relative;
}

.mobnone{display: none;}
header.site-header span.text,
.single-header-info span.theme-color {
    font-size: 14px;
    font-weight: 600;
    word-break: break-word;
}
header.site-header button.header-btn {
  border: none;
  background-color: #c9593f;
  border-radius: 30px;
  color: #ffffff;
  cursor: pointer;
  padding: 11px 37px;
  display: inline-block;
  line-height: 1.5em;
  font-weight: 600;
  font-size: 15px;
  outline: none;
  position: relative;
  opacity: 1;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  top: 10px;
  float: right;
  word-break: break-word;
}
header.site-header button.header-btn span {
  display: inline-block;
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  opacity: 1;
  -webkit-transition-delay: 0.3s;
  transition-delay: 0.3s;
}
header.site-header button.header-btn i {
  font-size: 20px;
  text-align: center;
  width: 100%;
  top: 50%;
  left: 0;
  display: block;
  position: absolute;
  opacity: 0;
  -webkit-transform: translate(0, -50%);
  transform: translate(0, -50%);
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  -webkit-transition-delay: 0s;
  transition-delay: 0s;
}
header.site-header button.header-btn:before {
  border-radius: 30px;
  content: '';
  top: 3px;
  bottom: 3px;
  left: 50%;
  right: 50%;
  position: absolute;
  background: rgba(255, 255, 255, 0);
  -webkit-transition: all 0.3s;
  transition: all 0.3s;
  -webkit-transition-delay: 0.15s;
  transition-delay: 0.15s;
}
header.site-header button.header-btn:hover,
header.site-header button.header-btn.hover {
  color: #000000;
}
header.site-header button.header-btn:hover span,
header.site-header button.header-btn.hover span {
  opacity: 0;
  -webkit-transition-delay: 0s;
  transition-delay: 0s;
}
header.site-header button.header-btn:hover i,
header.site-header button.header-btn.hover i {
  opacity: 1;
  -webkit-transition-delay: 0s;
  transition-delay: 0s;
}
header.site-header button.header-btn:hover:before,
header.site-header button.header-btn.hover:before {
  left: 3px;
  right: 3px;
  background: #ffffff;
  -webkit-transition-delay: 0s;
  transition-delay: 0s;
}
header.site-header button.header-btn:active:before {
  background: rgba(255, 255, 255, 0.7);
}
.navigation .mainmenu{ 
    position:relative; 
    z-index:9999; 
    vertical-align:middle; 
    float: none;
    text-align: center;
}
.navigation .mainmenu li{ 
    display:inline-block; 
    position:relative; 
    line-height:normal; 
    margin-right: 0px;
}
.navigation .mainmenu li:last-child{
    margin-right: 0;
}
.navigation .mainmenu li a{ 
    display:block; 
    text-align:center; 
    color:#fff; 
    position:relative; 
}
/*.mainmenu .sub-menu li a:after {
   content: "\f101";
    position: relative;
    float: left;
    margin-top: 2px;
    margin-left: 0px;
    -webkit-transition: all 0.2s ease;
    -moz-transition: all 0.2s ease;
    -o-transition: all 0.2s ease;
    -ms-transition: all 0.2s ease;
    transition: all 0.2s ease;
    opacity: 0;
    font-family: 'FontAwesome';
    speak: none;
    font-style: normal;
    font-weight: normal;
    font-variant: normal;
    text-transform: none;
    line-height: 1;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-size: 1em;
    padding-right: 5px;
}*/
.mainmenu .sub-menu li:hover a:after {
    opacity: 1;
}
/* = Navigation 2nd level css
-------------------------------------------------------------- */
ul.sub-menu{
    display:none;
    z-index:9999;
    position:absolute;
    left:-9999px;
    border-radius: 0px;
    box-shadow: 0 0 12px rgba(0,0,0,0.2);
    left: 0;
    top: 120%;
    text-align: left;
    background: #9bb70d;
    min-width: 250px;
    z-index: 999;
    padding: 8px;
    margin-top: 4px;
    box-shadow: 0px 5px 30px rgba(0,0,0,0.13);
}
.navigation .mainmenu li ul li{
    display:block; 
    position:relative; 
    float:none; 
    line-height:18px; 
    margin-left:0; 
    margin-right:0; 
    -webkit-transform: skew(0deg); 
    -ms-transform: skew(0deg); 
    transform: skew(0deg); 
}
.navigation .mainmenu li ul li a{
    display:block; 
    padding: 5px 0px;
    position:relative;
    top:0; 
    text-align:left; 
    z-index: 1;
}
.navigation .mainmenu li:hover ul.sub-menu li {
    opacity: 1;
    visibility: visible;
}
.navigation .mainmenu ul.sub-menu li a {
    font-size: 14px;
    position: relative;
    color: #3b3fb2;
    font-weight: 600;
    margin-right: 25px;
}
.navigation .mainmenu ul.sub-menu li a:hover {
    background: #fff;
    z-index: 200;
}
.navigation .mainmenu ul.sub-menu li a {
    list-style-type: none;
      font-size: 13px;
      font-weight: 400;
      padding: 12px 20px;
      display: inline-block;
      width: 100%;
      margin: 0px;
}
.ht-sticky-header .header-transparent {
    position: fixed;
    left: 0;
    right: 0;
}
.navigation .mainmenu ul.sub-menu li.current_page_item:hover a, 
ul.sub-menu .current_page_item:hover > a{
    background: none;
}

@media only screen and (max-width: 1300px) {
ul.sub-menu {
    position: relative;
    box-shadow: none;
}
.navigation .mainmenu ul.sub-menu li {
    opacity: 1;
    visibility: visible;
    -moz-transform: translate3d(0px, -12px, 0px);
}

}

@media only screen and (min-width: 1300px) {

.navigation .mainmenu ul.sub-menu li {
    list-style-type: none;
    font-size: 13px;
    /*color: #666;*/
}
.navigation .mainmenu li:hover > ul{
      display: block;
    width: 250px;
    position: absolute;
    left: auto;
    top: 85%;
    text-align: left;
}
 .navigation .mainmenu li:hover ul.sub-menu{
    left: auto;
    top: 47px;
    text-align: left;
    -ms-animation: zoomIn .3s ease-in-out;
    -webkit-animation: zoomIn .3s ease-in-out;
    animation: zoomIn .3s ease-in-out;
    padding: 0;
    border-radius: 0px;
}
}
/*header.site-header ul.sub-menu li a:before {
    position: absolute;
    content: '';
    top: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: 0;
    height: 100%;
    background-color: #ffe723;
    -webkit-transform-origin: left;
    -ms-transform-origin: left;
    transform-origin: left;
    -webkit-transition: width 0.3s ease-in-out;
    -ms-transition: width 0.3s ease-in-out;
    transition: width 0.3s ease-in-out;
    z-index: -1;
    border-radius: 5px;
}*/
header.site-header ul.sub-menu li a:hover::before {
    width: 100%;
}
/* = Navigation 3rd level css
-------------------------------------------------------------- */
.navigation .mainmenu li:hover ul li:hover > ul{
    display:block; 
    position:absolute; 
    left:205px; 
    top:-1px; 
    -ms-animation: fadeInRight .3s ease-in-out; 
    -webkit-animation: fadeInRight .3s ease-in-out; 
    animation:fadeInRight .3s ease-in-out; 
    z-index: 1;
}
.heade-border {
    border-bottom: 1px solid #939192;
    padding: 0 5px;
}
.header.site-header ul.sub-menu li a {
        font-size: 14px;
/*    transition: all 0.5s ease 0s;*/
    font-weight: 400;
    position: relative;
}
.header.site-header ul.sub-menu li a{   
    display: block;
    vertical-align: middle;
    -webkit-transition: all 0.2s ease;
    -moz-transition: all 0.2s ease;
    -o-transition: all 0.2s ease;
    -ms-transition: all 0.2s ease;
    transition: all 0.2s ease;
    line-height: 1.4em;
    letter-spacing: .05em;
    word-break: normal;
}
  .header.site-header ul.sub-menu li:hover a{
   background: none;
    /*color: #000;*/
    border-radius: 0px;
    overflow: hidden;
}
header.site-header ul {margin: 0;}
header.site-header .text-bx.padding0 {
    font-size: 13px;
    font-weight: 700;
}
header.site-header .list-inline.m-a0 {
   padding: 2px 0;
   float: right;
}
.navigation .mainmenu {
    position: relative;
}
.header-icon i.fa {
    position: relative;
    top: 0px;
    font-size: 15px;
    padding-right: 9px;
    font-weight: 700;
}
.site-header {
    position: relative;
    z-index: 99999;
    width: 100%;
}
.site-header ul,
.site-header ol {
    margin-bottom: 0;
}
/*without top bar*/
.extra-nav {
    float: right;
    padding: 26px 0;
    position: relative;
    z-index: 9;
}
.extra-nav .site-button-link{
    font-size:15px;
}
.extra-nav .extra-cell {
    display: inline-block;
    margin-left: 10px;
}
@media only screen and (max-width: 767px) {

    .extra-nav {
        margin-right: 15px;
    }
}
@media only screen and (max-width: 480px) {
    .extra-nav {
        margin: 0 1px 0 0;
        text-align: right;
    }
}
.new-page{
    padding: 2px 5px;
    font-size: 10px;
    background: #ff0000;
    color: #fff;
    border-radius: 4px;
    vertical-align: middle;
    margin-left: 2px;
}
.new-page.menu-new{
    font-size: 10px;
    position: absolute;
    right: 10px;
    top: 8px;
    padding: 3px 4px;
    line-height: 10px;
}
@media only screen and (max-width: 1200px) {
    .new-page.menu-new{
        display:none;
    }

}
/* map page header*/
#header-part.fix-map-header {
    height: 90px;
}
#header-part.fix-map-header .main-bar {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1;
}
@media only screen and (max-width: 480px) {
    #header-part.fix-map-header {
        height: 160px;
    }
}
@media screen and (max-width: 991px) {


#ht-contactus-wrap .contact_area {margin-bottom: 3em !important;}
#ht-contactus-wrap .con-inn-img {  margin-top: 0em !important;}
header.site-header input[type='search'], 
header.site-header input[type='search']:hover {width: 100%;}
#ht-contactus-wrap .address-c-box{margin:4em 1em 0 1em;}
main#innerpage-box #blog-box h2 {   font-size: 20px !important;}

#content-box .faqimg img {  height: 350px !important;}
#innerpage-box .item.innertest-item{width: 100% !important;}
#about,#service,#about-section,#blog,#featured-product-section,
#counter,#team{padding: 1em 0 !important;
}
}
header.page-main-header .innerpgimg{
    display: block;
    position: relative;
}
header.page-main-header .innerpgimg:after{
 content: "";
 position: absolute;
 left: 0;
 top: 0;
 bottom: 0;
 width: 100%;
 background: #000;
 transition: all 0.2s ease;
 -moz-transition: all 0.2s ease;
 -webkit-transition: all 0.2s ease;
 opacity: 0.6;
}
div#secondary .widget_calendar table thead tr th{
    padding: 10px;
}
.widget_calendar tfoot tr td a {
    padding: 2px 10px;
}
/*---------------------------------------------------------------
15. PAGE TOP BAR (left & right content)
---------------------------------------------------------------*/
.icon-x {
    text-align: right;
}
.main-header-info {
    text-align: center;
}
.header-icon {
    display: inline-block;
    text-align: left;
}
.top-bar {
    border-bottom: 1px solid rgba(0, 0, 0, 0.08);
    color: #444444;
}
.dlab-topbar-right {
    float: right;
}
.dlab-topbar-left ul,
.dlab-topbar-right ul{
    list-style:none;
    margin:0;
    padding:0;
}
.dlab-topbar-left ul li,
.dlab-topbar-right ul li{
    display:inline-block;
}
.dlab-topbar-left ul li{
    padding-right:0px;
}
.dlab-topbar-left ul li a,
.dlab-topbar-right ul li a{
    color:#444444;
}
.topbar-social li a{
    padding: 0 6px; 
}
@media only screen and (max-width: 991px) {
header .Reg{ text-align: center;font-size: 16px;}
.share-btn ul li a.site-button { margin: 10px 8px 0 8px;}
#about .abou-img1 img ,
#about .abou-img2 img,
#about .abou-img3 img{height: auto !important;}
.dlab-topbar-right {padding-left: 0;padding-right: 15px;}
.dlab-topbar-left{padding-right: 0;}
#innerpage-box .lz-gallery-images img{height: 250px !important;}
#innerpage-box .inser {   width: 50% !important;}
#about .abou-img1 img ,
#about .abou-img2 img {width: auto !important;}
/*#features-section .mem-inn { margin-bottom: 1em !important;}*/
.counter-area .count-box {  padding: 20px 10px;}
/*#team .our-team { padding: 5px 0;}*/
#team .our-team {padding-top: 0 !important;}
#team .team-social-icon { text-align: center; display: inline-block !important; text-align: center; width: 100%;}
#team .team-social-icon a {   display: inline-block !important;}
#appointment .appbtn a { top: 0em !important;
}
}
.site-main .comment-navigation,
.site-main .posts-navigation,
.site-main .post-navigation {
    margin: 0 0 15px;
    overflow: hidden;
}
.comment-navigation .nav-previous,
.posts-navigation .nav-previous,
.post-navigation .nav-previous {
    float: left;
    width: 50%;
}
.comment-navigation .nav-next,
.posts-navigation .nav-next,
.post-navigation .nav-next {
    float: right;
    text-align: right;
    width: 50%;
}

.navigation .mainmenu {
    transition: padding 0.3s ease;
    -moz-transition: padding 0.3s ease;
    -webkit-transition: padding 0.3s ease;
}
.navigation .mainmenu.ht-menu {
    display: block !important;
}
.navigation .mainmenu li {
 position: relative;
}
.navigation .mainmenu li a {
  display: block;
  text-decoration: none;
  font-size: 15px;
  padding: 2px 0;
}

.sf-arrows .sf-with-ul {
    padding-right: 0em !important;
}

.justify-content-end {
    -ms-flex-pack: end!important;
    justify-content: flex-end!important;
}

i.flaticon-phone-call.m-r5, 
i.ti-location-pin.m-r5 {
    margin-right: 12px;
}
/* = Navigation sticky header css
-------------------------------------------------------------- */

.top-bar-head {
    position: relative;
    width: 100%;
}

.header-transparent .sticky-header {
    padding: 14px 0;
    text-align: right;
}
.header-seo.header-transparent .is-fixed .main-bar ul {
    padding: 5px 0 10px;
    text-align: center;
}
.header-seo.header-transparent .is-fixed .main-bar ul ul.sub-menu {
    padding: 0px;
}
.header-seo.header-transparent .is-fixed .main-bar input[type="search"] {
    top: 10px;
    position: relative;
}
.header-seo.header-transparent .is-fixed .main-bar span.sb-icon-search {
    top: 8px;
}
header.site-header .dlab-topbar-left {
    position: relative;
    top: 13px;
}

/*.share-btn ul li{
    position: relative;
    overflow: hidden;
}*/
header.site-header ul.sub-menu li a, 
.navigation .mainmenu li.current_page_item ul.sub-menu li a, 
.header.site-header ul.sub-menu li a {
    background: none;
}


.header.site-header ul.sub-menu li a:hover {
    padding-left: 35px;
   /* text-align: left;*/
    transition: all 0.3s ease;
}

.share-details-btn ul{
    margin:0;
    padding:0;
    list-style:none;
}
.share-details-btn ul li{
    display:inline-block;
    margin-right: 5px;
    margin-bottom: 6px;
}
/*----------------------------------------*/
/*  Slider Area
/*----------------------------------------*/

.slider_section{
    position: relative;
}
.slider_section .owl-dots {
    display: none;
}
.ht-slide{
    position: relative;
}
.ht-slide img{
    width: 100%;
}
.slider_gradiant{
    position: absolute;
    left: 0;
    right: 0%;
    top: 0;
    bottom: 0;
    background: #fff;
    opacity: 0.5;
    width: 35%;
}
.slider_content {
    position: absolute;
    top: 42%;
    left: 43%;
    width: 560px;
    margin-left: -527px;
    -ms-transform: translateY(-50%);
    -webkit-transform: translateY(-50%);
    transform: translateY(-50%);
    opacity: 1;
    /*text-align: center;*/
    z-index: 99;
}
.slider_section .title {
    font-size: 50px;
    font-weight: 600;
    word-break: break-word;
    line-height: 55px;
}
.slider_section .sub-title {
    font-size: 18px;
    font-weight: 500;
    line-height: 30px;
    word-break: break-word;
    padding: 0em 0em;
    margin: 2em 0;
    /* width: 470px;*/
}
/*====== btn ====*/
.slider_section .btn5 {
    margin-top: 2em;
}
.slider_section .btn5 a {
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-decoration: none;
    font-weight: 500;
    font-size: 18px;
    padding: 10px 30px;
    color: #fff;
    transition: all 0.5s;
    border-radius: 0px;
    cursor: pointer;
    text-align: center;
    z-index: 9;
    max-width: 12em;
   word-break: break-all;
    background-color: #000;
    border: none;
}
.slider_section .btn5 a:after {
  position: absolute;
  content: "";
  width: 100%;
  height: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  border-radius: 0px;
   background-color: #f3f;
/*background-image: linear-gradient(315deg, #4dccc6 0%, #96e4df 74%);*/
  transition: all 0.3s ease;
}
.slider_section .btn5 a:hover {
  color: #fff;
}
.slider_section .btn5 a:hover:after {
  top: 0;
  height: 100%;
}
.slider_section .btn5 a:active {
  top: 2px;
}

/*====================*/
#slider .owl-carousel{
    display: block;
}
.slider_section .owl-theme .owl-dots {
    position: absolute;
    bottom: 50%;
    right: 0px;
    display: none;
}
.slider_section .owl-nav{
    display: block;
}
.slider_section .owl-nav .owl-prev {
    position: absolute;
    left: 90px;
    top: 45%;
    opacity: 0;
    -webkit-transition: all 0.4s ease-out;
    transition: all 0.4s ease-out;
    background: rgba(0, 0, 0, 0.5);
    width: 50px;
    line-height: 50px;
    height: 50px;
    position: absolute;
    display: block;
    z-index: 1000;
    border-radius: 0%;
    cursor: pointer;
}
.slider_section .owl-nav .owl-next {
    position: absolute;
    right: 90px;
    top: 45%;
    opacity: 0;
    -webkit-transition: all 0.4s ease-out;
    transition: all 0.4s ease-out;
    background: rgba(0, 0, 0, 0.5) ;
    width: 50px;
    height: 50px;
    line-height: 50px;
    position: absolute;
    display: block;
    z-index: 1000;
    border-radius: 0%;
    cursor: pointer;
}
.slider_section .owl-nav .owl-prev span,
.slider_section .owl-nav .owl-next span {
    font-size: 65px;
    color: #fff;
    line-height: 45px;
    font-weight: 300;
}
.slider_section .owl-nav .owl-prev:focus,
.slider_section .owl-nav .owl-next:focus {
    outline: 0;
}
.slider_section .owl-nav .owl-prev:hover,
.slider_section .owl-nav .owl-next:hover {
    background: #000 !important;
}
.slider_section:hover .owl-prev {
    left: 0px;
    opacity: 1;
}
.slider_section:hover .owl-next {
    right: 0px;
    opacity: 1;
}
/*----------------------------------------*/
/*   Counter Area
/*----------------------------------------*/
.counter-area {
    position: relative;
    overflow: hidden;
}
.counter-area .ovly{
    position: absolute;
    background: #000;
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    opacity: 0.5;
}
.counter-area .cd-single {
    margin-bottom: 6px;
}
.counter-area .count-box{
    position: relative;
    transition: all 0.3s ease 0s;
    padding: 20px 45px;
    background: #f3f3;
    /* border: 2px solid #000; */
    border-radius: 0px;
    text-align: center;
}

.counter-area .count-box:before {
    content: "";
    border-top: 0 solid #a0c716;
    border-right: 0 solid transparent;
    position: absolute;
    left: 0;
    top: 0;
    z-index: 1;
    transition: all 0.3s ease 0s;
}

.counter-area .count-box:hover:before {
    border-top-width: 50px;
    border-right-width: 50px;
}

.counter-area .count-box:after {
    content: "";
    border-bottom: 0 solid #a0c716;
    border-left: 0 solid transparent;
    position: absolute;
    bottom: 0;
    right: 0;
    z-index: 1;
    transition: all 0.3s ease 0s;
}
.counter-area .count-box:hover:after {
    border-bottom-width: 50px;
    border-left-width: 50px;
}
/*==================*/
.couneter-box .cd-icon i{
    font-size: 55px;
    transition: all 0.3s ease 0s;

}
.counter-area .count-box:hover .cd-icon i{
    transform: rotateY(180deg);
    transition: all 0.3s ease 0s;
}
.counter-area .cd-num {
    font-size: 48px;
    color: #fff;
    font-weight: 700;
    transition: all 0.3s ease 0s;

}
.counter-area .cd-title {
    font-size: 20px;
    color: #fff;
    font-weight: 400;
    padding: 15px 0 0 0;
    text-align: center;
    transition: all 0.3s ease 0s;
}
/*.counter-area .count-box:hover .cd-title{
    font-weight: 600;
    transition: all 0.3s ease 0s;
}*/

/*=====================================*/
/*=============about Section=================*/
/*=====================================*/

#about {
    position: relative;
    overflow: hidden;
}

#about .section-title h2{font-size: 36px;}
#about .htext{font-size: 16px;line-height: 24px;}
#about .about-area-data h4{
    font-size: 30px;
    margin: 0;
    font-weight: 600;
}
#about .about-area-data p {
   font-size: 16px;
    line-height: 24px;
}
#about .aboutus-single {
    margin-top: 2em;
}
/*===============================*/
#about .hi-icon {
    text-align: center;
    background: #fafd15;
    width: 50px;
    height: 50px;
    line-height: 50px;
    border-radius: 50px;
    font-size: 22px;
    display: inline-block;
    cursor: pointer;
    margin: 0 auto;
    position: relative;
    z-index: 1;
    -webkit-transition: -webkit-transform ease-out 0.1s, background 0.2s;
    -moz-transition: -moz-transform ease-out 0.1s, background 0.2s;
    transition: transform ease-out 0.1s, background 0.2s;
}

#about .aboutus-single:hover .hi-icon {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
}
#about .hi-icon span:after {
        content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: transparent;
    transform: rotate(45deg);
    z-index: -1;
    transition: all 0.5s ease-in-out 0s;
}

#about .aboutus-single:hover .hi-icon span:after {
    transform: rotate(-45deg);
        background: #f3f;
}
#about .aboutus-single:hover .hi-icon span{
    font-size: 20px;
}
#about .aboutus-single:hover .hi-icon {
    width: 45px;
    height: 45px;
}

#about .aboutus-single:hover .hi-icon {
    background: none;
}
/*===============================*/
#about .abtimg {  padding: 0 3em 0 0;}
#about .abtimginn{border-right:4px solid #fff ;}
#about .abou-img1 img {
    border-bottom: 4px solid #fff;
}
#about .abou-img1 img ,
#about .abou-img2 img {
    width: 100%;
    height: 242px;
    border-radius: 5px;
}

#about .abou-img3 img {
    height: 483px;
}

/*=====================================*/
/*=============gallery Section=================*/
/*=====================================*/

#gallery {
    position: relative;
    overflow: hidden;
}
#gallery .section-title .border1 {
    display: block;
    position: relative;
    margin: 1em auto 0;
    content: " ";
    text-shadow: none;
    width: 140px;
    border-radius: 50px;
    border-style: solid;
    border-width: 6px;
    border-left: none;
    border-right: none;
    border-bottom: none;
    border-color: #c9593f;
}
#gallery .section-title h2{font-size: 36px;}
#gallery .htext{font-size: 16px;line-height: 24px;}
#gallery .gallery-area-data h4{
    font-size: 30px;
    margin: 0;
    font-weight: 600;
}
#gallery .gallery-area-data p {
   font-size: 16px;
    line-height: 24px;
}
#gallery .galleryus-single {
    margin-top: 2em;
}
/*===============================*/
#gallery .hi-icon {
    text-align: center;
    background: #fafd15;
    width: 50px;
    height: 50px;
    line-height: 50px;
    border-radius: 50px;
    font-size: 22px;
    display: inline-block;
    cursor: pointer;
    margin: 0 auto;
    position: relative;
    z-index: 1;
    -webkit-transition: -webkit-transform ease-out 0.1s, background 0.2s;
    -moz-transition: -moz-transform ease-out 0.1s, background 0.2s;
    transition: transform ease-out 0.1s, background 0.2s;
}

#gallery .galleryus-single:hover .hi-icon {
    -webkit-transform: scale(1.0);
    -moz-transform: scale(1.0);
    -ms-transform: scale(1.0);
    transform: scale(1.0);
}
#gallery .hi-icon span:after {
        content: "";
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    background: transparent;
    transform: rotate(45deg);
    z-index: -1;
    transition: all 0.5s ease-in-out 0s;
}

#gallery .gallery-single:hover .hi-icon span:after {
    transform: rotate(-45deg);
        background: #f3f;
}
#gallery .gallery-single:hover .hi-icon span{
    font-size: 20px;
}
#gallery .gallery-single:hover .hi-icon {
    width: 45px;
    height: 45px;
}

#gallery .gallery-single:hover .hi-icon {
    background: none;
}
/*===============================*/
#gallery .abtimg {  
    display: block;
}
#gallery .abtimginn{border-right:4px solid #fff ;}

#gallery .abou-img2 img ,
#gallery .abou-img3 img {
    width: 100%;
    max-height: 242px;
    border-radius: 5px;
}

#gallery .abou-img img {
    width: 100%;
    border-radius: 5px;
}
#gallery .about-img-text {
    margin: 15px auto;
    text-align: center;
}
#gallery .about-btn {
    text-align: center;
}
#gallery .btn5 a {
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    padding:4px 6px;
    color: #fff;
    transition: all 0.5s;
    border-radius: 0px;
    cursor: pointer;
    text-align: center;
    z-index: 9;
    word-break: break-all;
    background-color: #c9593f;
    border: none;
    margin-top: 1.5em;
}
#gallery .btn5 a:after {
  position: absolute;
  content: "";
  width: 100%;
  height: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  border-radius: 0px;
   background-color: #4dccc6;
/*background-image: linear-gradient(315deg, #4dccc6 0%, #96e4df 74%);*/
  transition: all 0.3s ease;
}
#gallery .btn5 a:hover {
  color: #fff;
}
#gallery .btn5 a:hover:after {
  top: 0;
  height: 100%;
  background-color: #000;
}
#gallery .btn5 a:active {
  top: 2px;
}
#gallery .btn5 a i {
    color: #000;
    padding: 5px 10px;
    background: #fff;
    margin-left: 8px;
    font-size: 20px;
    font-weight: 300;
}

/*----------------------------------------*/
/*   Service Area
/*----------------------------------------*/
.service-area {
    position: relative;
    overflow: hidden;
}

.service-area .single-service-bx {    
    position: relative;
    transition: all 0.5s;
    margin-bottom: 2em;
    padding: 0;
}

.service-area .single-service {
    position: relative;
    border: 1px solid #f3f3;
    transition: all 0.5s;
}
.service-area .ser-img img {
    border: 8px solid #000;
    border-bottom: none;
    width: 100%;
    /*height: 250px;*/
    transition: all 0.3s;
}
.service-area .box-border{
    display: block;
    position: relative;
    content: " ";
    text-shadow: none;
    width: 45%;
    border-radius: 0 50px 50px 0;
    border-style: solid;
    border-width: 4px;
    border-color: #000;
    transition: all 0.5s;
}
.service-area .single-service:hover .box-border{
    width: 100%;
    transition: all 1s;
    border-radius: 0;
}
.service-area .service-title-box {
    position: relative;
    padding:30px 14px 10px 14px;
}
.service-area .service-title-box h3{
    font-size: 20px;
    font-weight: 700;
    transition: all 0.5s ease;
    position: relative;
    margin: 0;
}
.service-area .service-title-box p {
    font-size: 16px;
    padding: 10px 0;
    line-height: 24px;
    margin: 0;
}

.service-area .btn5 a {
    clip-path: polygon(25% 0, 100% 0%, 100% 100%, 0 100%);
    float: right;
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-decoration: none;
    font-weight: 500;
    font-size: 16px;
    padding: 12px 20px 12px 50px;
    color: #fff;
    transition: all 0.5s;
    border-radius: 0px;
    cursor: pointer;
    text-align: center;
    z-index: 9;
    max-width: 12em;
   word-break: break-all;
    background-color: #89d8d3;
    border: none;
}
.service-area .btn5 a i{
    font-size: 17px;
    padding-left: 5px;
    font-weight: 600;
    top: 2px;
    position: relative;
}
.service-area .btn5 a:after {
  position: absolute;
  content: "";
  width: 100%;
  height: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  border-radius: 0px;
   background-color: #f3f;
  transition: all 0.3s ease;
}
.service-area .btn5 a:hover {
  color: #fff;
}
.service-area .btn5 a:hover:after {
  top: 0;
  height: 100%;
}
.service-area .btn5 a:active {
  top: 2px;
}

/*=============service slider==============*/
/*.service-area .owl-carousel button.owl-dot{display: none;}
.service-area .owl-theme .owl-nav {  margin-top: 0px;}
.service-area .owl-carousel{display: block;}
.service-area .owl-carousel .owl-nav{display: none;}
.service-area .owl-carousel .owl-nav .owl-next, 
.service-area .owl-carousel .owl-nav .owl-prev{
    background: #3ff;
    color: inherit;
    width: 44px;
    height: 44px;
    line-height: 40PX;
    border-radius: 50px;
    font-size: 50px;
    text-align: center;
    font-weight: 200;
    position: absolute;
    top: 30%;
}
.service-area .owl-carousel .owl-prev {
    float: left;
    left: -59px;
}
.service-area .owl-carousel .owl-next{
    float: right;
    right: -59px;
}*/

/*==========================*/
/*----------------------------------------*/
/*   Team Section
/*----------------------------------------*/
#team{
    position: relative;
    overflow: hidden;
}
#team .single-team {
    margin-bottom: 2em;
}
#team .our-team {
    padding-top: 2.5em;
    background: #f3f;
    transition: all 0.5s;
}
#team .our-team .single-team-img {
    overflow: hidden;
    position: relative;
}
#team .our-team .single-team-img img{
    width: 100%;
    height: 300px;
}

#team .single-team-img:before,
#team .single-team-img:after{
    content: "";
    background-color: #ff3f;
    height: 100%;
    width: 100%;
    opacity: 0.7;
    position: absolute;
    top: 0;
    right: -100%;
    z-index: 1;
    transition: all 0.4s;
}
#team .our-team:hover .single-team-img:after{
    opacity: 0;
    transform: scale(0.9,0.7);
    right: 0;
    transition: all 0.4s;
}
#team .our-team:hover .single-team-img:before{ right: 100%; }
#team .our-team:hover .single-team-img:after{
    opacity: 0.3;
    transform: scale(1);
}
#team .team-social-icon {
    position: relative;
    display: grid;
    transform: rotateZ(180deg);
}
#team .team-social-icon a {
    display: block;
    text-align: center;
    bottom: 0;
    position: relative;
    margin: 5px;
    
}
#team .team-social-icon i {
    font-size: 18px;
    transition: all 0.1s;
    transform: rotateZ( 180deg);
    margin: 4px 0;
}
#team .team-text h4{
    font-size: 20px;
    font-weight: 700;
    margin-bottom: 0px;
    padding: 10px 0;
}
#team .team-designation{
    font-size: 16px;
    font-weight: 500;
}
#team .team-con p {
    color: #000;
    font-size: 16px;
    padding: 10px 0;
    display: none;
}


/*==================feature products Section ==================*/
/*========================================== ==================*/

#featured-product-section{
    position: relative;
    overflow: hidden;
}
#featured-product-section .product-grid{
    position: relative;
    overflow: hidden;
    transition: all 1s ease 0s;
    margin: 1em 0;
    background: #f3f3f3;
    padding: 10px;
    border: 1px solid #000;
}
#featured-product-section .product-grid .product-image{
    overflow: hidden;
    position: relative;
}

#featured-product-section .product-image img {
    position: relative;
    width: 100%;
    height: 250px;
    position: relative;
    top: 0px;
    transition: all 0.5s;
    border-radius: 0px;
}
#featured-product-section .product-grid .sale {
     position: absolute;
    left: 0;
    top: 30px;
    background: #0de9df;
    color: #343434;
    font-size: 13px;
    text-transform: capitalize;
    font-weight: 600;
    padding: 3px 15px;
    line-height: 17px;
    z-index: 999;
}
#featured-product-section .product-grid .price{
    color: #ffffff;
    font-size: 16px;
    background: #212121;
    padding: 5px 10px;
    font-weight: 600;
}
#featured-product-section .product-grid .quick-view{    
   opacity: 1;
    position: absolute;
    bottom: 0%;
    padding: 0 8px;
    right: 0%;
    font-size: 25px;
    z-index: 2;
}
#featured-product-section .product-content {
    position: relative;
    padding: 10px 0px;
    /* background: #f3f3; */
    transition: all 1s ease 0s;
   /* width: 85%;
    float: right;
    text-align: center;*/
}
#featured-product-section .product-grid h3.title {
    color: #151515;
    font-size: 22px;
    font-weight: 400;
    word-break: break-word;
    margin: 8px 0 15px 0;
}
#featured-product-section ins{
    background: transparent;
    font-size: 20px;
    color: #222222;
    font-weight: 400;
    padding-left: 10px;
}
#featured-product-section bdi span{padding-right: 5px;}
#featured-product-section .product-grid bdi {
    font-size: 20px;
    font-weight: 400;
}
#featured-product-section .Section-btn {
    text-align: center;
    position: absolute;
    top: 20%;
    opacity: 0;
    transition: all 1s ease 0s;
    width: 100%;
    padding: 0 1em;
}
#featured-product-section .product-grid:hover .Section-btn{
    opacity: 1;
    transition: all 1s ease 0s;
}
/*==============btn 1=============*/
#featured-product-section .btn5 .view-more{
    margin-bottom: 8px;
    padding: 12px 40px;
    position: relative;
    display: inline-block;
    overflow: hidden;
    background: #f3f3;
    font-size: 14px;
    font-weight: 500;
    border: none;
    word-break: break-all;
    white-space: normal;
    transition:all 0.5s;
    z-index: 2;
}
/*==============btn 2=============*/
#featured-product-section .btn5 .more-button{
    padding: 12px 20px;
    position: relative;
    display: inline-block;
    overflow: hidden;
    background: #f3f3;
    font-size: 14px;
    font-weight: 500;
    border: none;
    white-space: normal;
    word-break: break-all;
    transition:all 0.5s;
    z-index: 2;
}


#featured-product-section .owl-carousel{
    display: block;
}
#featured-product-section .owl-carousel .owl-dots {  display: none;}
#featured-product-section .owl-carousel .owl-nav{  display: block;}

#featured-product-section .owl-nav .owl-prev,
#featured-product-section .owl-nav .owl-next {
    position: absolute;
    top: 40%;
    opacity: 1;
    -webkit-transition: all 0.4s ease-out;
    transition: all 0.4s ease-out;
    background: rgba(0, 0, 0, 0.5);
    width: 50px;
    line-height: 75px;
    height: 80px;
    position: absolute;
    display: block;
    z-index: 1000;
    border-radius: 0%;
    cursor: pointer;
}
#featured-product-section .owl-nav .owl-prev{
     left: -4em;
}
#featured-product-section .owl-nav .owl-next{
     right: -4em;
}

#featured-product-section .owl-nav .owl-prev span,
#featured-product-section .owl-nav .owl-next span {
    font-size: 60px;
    color: #fff;
    /*line-height: 40px;*/
    font-weight: 300;
}

/*----------------------------------------*/
/* features Section
/*----------------------------------------*/

#features-section{
    position: relative;
    overflow: hidden;
    text-align: center;
}
#features-section .owl-carousel{display: block !important;}
#features-section .mem-inn {
    position: relative;
    overflow: hidden;
    transition: all 0.3s;
    background: #f1f1f1;
    padding: 1.5em 1em;
    text-align: center;
    z-index: 1;
}

/*#features-section .features-inn:nth-child(even) .mem-inn{
    position: relative;
    background: #f3f3f3 !important;
}*/
/*#features-section .item .mem-inn:nth-child(2) .single-service{background: #fff;}*/

#features-section .mem-inn:after {
  position: absolute;
  content: "";
  width: 100%;
  height: 0;
  bottom: 0;
  left: 0;
  z-index: 0;
  border-radius: 0px;
   background-color: #4dccc6;
/*background-image: linear-gradient(315deg, #4dccc6 0%, #96e4df 74%);*/
  transition: all 0.3s ease;
}

#features-section .mem-inn:hover:after {
  top: 0;
  height: 100%;
}
#features-section .sec-icn {
    position: relative;
}
#features-section .sec-icn span{ 
    font-size: 60px;
    position: relative;
    z-index: 1; 
}
#features-section .features-content h3 {
    position: relative;
    z-index: 1;
    font-weight: 500;
    font-size: 18px;
    margin-bottom: 1.2em;
}
#features-section .features-content p {
    position: relative;
    z-index: 1;
    font-size: 16px;
    line-height: 25px;
    margin: 0;
}
#features-section .owl-nav,
#features-section .owl-dots{display: none;}

/*----------------------------------------*/
/*  appointment Area
/*----------------------------------------*/

#appointment {position: relative;overflow: hidden;}
#appointment .overlay{
    position:absolute; 
    left: 0;
    right: 0;
    top: 0;
    bottom: 0;
    background: #000;
    opacity: 0.4;
}
#appointment .ht-inquiry-member-wrap {
    position: relative;
    overflow: hidden;
    padding-left: 3em;
    padding-right: 3em;
    border: 1px solid #000;
    border-left: none;
    border-right: none;
}
#appointment .section-title {margin-bottom: 4em;}


.ht-inquiry-member-wrap input[type="text"], 
.ht-inquiry-member-wrap input[type="email"], 
.ht-inquiry-member-wrap input[type="url"], 
.ht-inquiry-member-wrap input[type="password"], 
.ht-inquiry-member-wrap input[type="search"], 
.ht-inquiry-member-wrap input[type="number"], 
.ht-inquiry-member-wrap input[type="tel"], 
.ht-inquiry-member-wrap input[type="range"], 
.ht-inquiry-member-wrap input[type="date"], 
.ht-inquiry-member-wrap input[type="month"], 
.ht-inquiry-member-wrap input[type="week"], 
.ht-inquiry-member-wrap input[type="time"], 
.ht-inquiry-member-wrap input[type="datetime"],
.ht-inquiry-member-wrap input[type="datetime-local"], 
.ht-inquiry-member-wrap input[type="color"],
.ht-inquiry-member-wrap input[type="file"],
.ht-inquiry-member-wrap textarea{    
    border: 1px solid #fff !important;
    z-index: 2000;
    width: 100%;
    color: #fff;
    margin: 0 0 20px 0;
    border-radius: 0;
    padding:22px 20px;
    font-size: 16px;
    height: 40px;
    font-weight: 500;
    background:transparent;
    /* box-shadow: -1px 0px 10px rgba(50,50,0,0.1); */
}
.ht-inquiry-member-wrap input[type="file"] {cursor: pointer;padding: 12px 20px;}
.ht-inquiry-member-wrap select{
    border: none;
    z-index: 2000;
    width: 100%;
    color: #595959;
    margin: 0 0 25px 0;
    border-radius: 10px;
    padding: 16px 20px;
    font-size: 14px;
    height: 100px;
    background: rgba(0, 0, 0, 0.8);
}
.ht-inquiry-member-wrap textarea {
    height: 110px;
    padding: 18px 20px;
    margin-top: 0px;
}
.ht-inquiry-member-wrap textarea::placeholder{color:#fff;}

.ht-inquiry-member-wrap label{color:#fff;margin:0 0 1em;width: 100%;}
.ht-inquiry-member-wrap p{font-size: 16px;}
.ht-inquiry-member-wrap input[type="submit"]{
    width: 100%;
    position: relative;
    background: #e1e1e1;
    padding: 15px 20px;
    font-size: 18px;
    font-weight: 600;
    color: #fff;
    border-radius: 0;
    margin-top: 2em;
    vertical-align: middle;
    -webkit-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    word-break: break-all;
}

/*.ht-inquiry-member-wrap input[type="submit"]:hover, 
.ht-inquiry-member-wrap input[type="submit"]:focus, 
.ht-inquiry-member-wrap input[type="submit"]:active {
    animation-name: shake;
    animation-duration: 3s;
    animation-iteration-count: infinite;
    animation-timing-function: ease-in;
}
*/
.ht-inquiry-member-wrap div.wpcf7 .ajax-loader {
    visibility: hidden;
    display: initial;
    background-image: url(../../images/ajax-loader.gif);
    width: 16px;
    height: 16px;
    border: none;
    padding: 0;
    margin: 0 0 0 4px;
    vertical-align: middle;
}
#appointment .app-rhsbx {
    padding:6em 3em 1em 3em;
}
#appointment .olyinn{
    position: absolute;
    background: #000;
    opacity: 0.3;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
}
#appointment h5{
    font-size: 42px;
    color: #fff ;
    font-weight: 700;
}
#appointment .app3-img img{
    width: 100%;
}
#appointment .appbtn a i{
    margin-right: 10px;
    width: 60px;
    height: 60px;
    line-height: 60px;
    background: #fff;
    color: #000;
    font-weight: 600;
    border-radius: 50px;
    transition: all 0.5s;
    /*transform: rotateY(180deg);*/
}
#appointment .appbtn a:hover i{
    transition: all 0.5s;
    transform: rotateY(180deg);
}

#appointment .appbtn a {
    margin-top: 2em;
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-decoration: none;
    font-weight: 600;
    font-size: 34px;
    padding:6px 30px 6px 8px;
    color: #fff;
    transition: all 0.5s;
    border-radius: 50px;
    cursor: pointer;
    text-align: center;
    z-index: 9;
    /* max-width: 12em; */
    word-break: break-all;
    background-color: #89d8d3;
    border: none;
}
#appointment .appbtn a:after {
  position: absolute;
  content: "";
  width: 100%;
  height: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  border-radius: 50px;
   background-color: #4dccc6;
/*background-image: linear-gradient(315deg, #4dccc6 0%, #96e4df 74%);*/
  transition: all 0.3s ease;
}
#appointment .appbtn a:hover {
  color: #fff;
}
#appointment .appbtn a:hover:after {
  top: 0;
  height: 100%;
}
#appointment .appbtn a:active {
  top: 2px;
}

/*----------------------------------------
/* Blog Area
/*----------------------------------------*/
.blog-area {position: relative;overflow: hidden;}
.blog-area .blog-post {  margin-bottom: 1.5em;}

.blog-area li{  list-style: none;}

.blog-area .blog-single {
    padding: 1.2em 1em 1em 1em;
    position: relative;
    overflow: hidden;
    transition: all .5s;
    border: 1px solid #000;
    border-top: none;
}
.blog-area .inner-area-title {   
    position: relative;
    font-size: 18px;
    font-weight: 700;
    word-break: break-word;
    padding: 0 0 0em;
    line-height: 30px;
    margin: 15px 0;
}

.blog-area .section-area-text {font-size: 16px;margin-bottom: 0;font-weight: 400;margin-top: 12px;}
.blog-area .blog-thumbnail{position: relative;z-index: 0;overflow: hidden;}
.blog-area .blog-thumbnail img{width: 100%; transition: all .5s ease;}
.blog-area .blog-thumbnail:before,
.blog-area .blog-thumbnail:after{
    content: "";
    background-color: #ff3f;
    height: 100%;
    width: 100%;
    opacity: 0.7;
    position: absolute;
    top: 0;
    left: -100%;
    z-index: 0;
    transition: all 0.4s ease-out 0.1s;
}
.blog-area .blog-post:hover .blog-thumbnail:after{
    opacity: 0;
    transform: scale(0.9,0.7);
    left: 0;
    transition: all 0.3s ease-out 0s;
}
.blog-area .blog-post:hover .blog-thumbnail:before{ left: 100%; }
.blog-area .blog-post:hover .blog-thumbnail:after{
    opacity: 0.3;
    transform: scale(1);
}
/*=========button===========*/

.blog-area .btn5 a {
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    padding:4px 6px;
    color: #fff;
    transition: all 0.5s;
    border-radius: 0px;
    cursor: pointer;
    text-align: center;
    z-index: 9;
    word-break: break-all;
    background-color: #f3f;
    border: none;
    margin-top: 1.5em;
}
.blog-area .btn5 a:after {
  position: absolute;
  content: "";
  width: 100%;
  height: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  border-radius: 0px;
   background-color: #4dccc6;
/*background-image: linear-gradient(315deg, #4dccc6 0%, #96e4df 74%);*/
  transition: all 0.3s ease;
}
.blog-area .btn5 a:hover {
  color: #fff;
}
.blog-area .btn5 a:hover:after {
  top: 0;
  height: 100%;
}
.blog-area .btn5 a:active {
  top: 2px;
}
.blog-area .btn5 a i {
    padding: 5px 10px;
    background: #000;
    margin-left: 8px;
    font-size: 20px;
    font-weight: 300;
}
/*----------------------------------------*/
/*   Footer Area
/*----------------------------------------*/
.footer-area .fborder {
    /*border-top: 3em solid #fff;*/
    top: 0;
    left:0 ;
    right: 0;
    bottom: 0;
    opacity: 0.3;
    background: #000;
    position: absolute;
    width: 100%;
}
.footer-area p { word-break: break-word;}
.widget-area ul.sub-menu:before,.widget-area ul.sub-menu:after,
.footer-area ul.sub-menu:before,.footer-area ul.sub-menu:after{display: none;}
.gallery-icon.landscape {margin: 6px;}
.footer-area {
    position: relative;
    overflow: hidden;
    background: #33393f;
}
.footer-area .gallery-icon img{height: 85px;}
/*.footer-area .f1,
.footer-area .top-area {
    border-bottom: 1px solid #fff;
}*/
.footer-area li {display: block;margin-bottom: 20px;font-size: 15px;font-weight: 300; }
.footer-area li a {
    font-weight: 300;
    text-decoration: none;
    font-size: 16px;
}
.footer-area ul {margin: 0 0 15px 00px;}
.footer-area p {    
    margin-bottom: 18px;
    font-weight: 300;
    line-height: 1.8;
    font-size: 16px;
}
.footer-area .current_page_item > a:before{display: none;}
.footer-area .current_page_item > a{
    background: none;
    font-weight: 500;
}
#footer.footer-area .widget-title {   
    letter-spacing: 0px;
    color: #feffff;
    margin-bottom: 1.5em;
    font-family: inherit;
    font-size: 22px;
    font-weight: 400;
}
#footer.footer-area .widget-title:after {
    content: "";
    display: block;
    width: 35%;
    height: 3px;
    margin: 0;
    position: relative;
    top: 8px;
    background: #d3d;
}
.footer-area .social-profile-icons ul{float: left;margin: 0;text-align: center;}
.footer-area .social-profile-icons ul li{
    display: inline-block;
    margin: 9px;
    width: 50px;
    height: 50px;
    line-height: 62px;
    border-radius: 10px;
    /* border: 1px solid #fff; */
    background: #000;
    transition: all 0.5s;
}
.footer-area .social-profile-icons ul li i{
    text-align: center;
    font-size: 28px;
    transition: all 0.5s;
}
.footer-area .social-profile-icons ul li:hover i{
    transform: rotateY(180deg);
    transition: all 0.5s;
}
.footer-area .social-profile-icons ul li:before{
    display: none;
}
.footer-area .widget {margin-bottom: 1.5em;}
.footer-bottom-area ul li {display: inline-block;margin-left: 40px;margin-bottom: 0;position: relative;}
.footer-area .bottom-area { 
    padding: 10px 0;
    background: #000;
}
.footer-area .bottom-area ul { margin-bottom: 0;}
.footer-area .widget.widget_recent_entries li a {font-size: 16px;}
.footer-text, .footer-text a {
    font-size: 16px;
    font-weight: 400;
    text-align: center;
    word-break: break-word;
    background: #000;
    padding: 20px 5px;
    transition: all 0.5s;
}
.footer-area .f-contact,
.footer-area .f-contact a {
    font-size: 16px;
    font-weight: 400;
    word-break: break-word;
    transition: all 0.3s;
    color: #fff;
}
.footer-area .f-contact-inn{
    margin: 20px 0px;
}
.footer-area .f-contact i{
    font-size: 18px;
    color: #fff;
}
.footer-area .pd-0{padding: 0;}
.footer-area .bottom-area li a:hover, .social-profile-icons li a:hover{border-bottom: 0;}
.footer-area .bottom-area li:hover a::after, .footer-area .bottom-area li:hover a:after {width: 20px;}
.footer-area .bottom-area li a:after, .footer-area .bottom-area li a:after {position: absolute;width: 0;height: 2px;background: #ff8b00;content: "";bottom: -5px;left: 2px;transition: .3s;}
.footer-area .bottom-area li.current_page_item a:after{position: absolute;width: 20px;height: 2px;background: #ff8b00;content: "";bottom: -5px;left: 2px;transition: .3s;}

.footer-area .bottom-area .footer-link {
    text-align: right;
}
.footer-area  ul {
    list-style: none;
    margin: 10px 0 0 0px;
    padding: 0;
    position: relative;
    background: none !important;
    box-shadow: none !important;
    display: block;
    z-index: 1;
}

.single-footer ul li:before {
    content: "\f178";
    font-family: 'Fontawesome';
    font-size: 18px;
    padding-right: 10px;
    font-weight: 400;
}
.footer-area .f1 i {
    font-size: 25px;
}
.footer-area input[type="text"], .footer-area input[type="email"], .footer-area input[type="url"], .footer-area input[type="password"], .footer-area input[type="search"], .footer-area input[type="number"], .footer-area input[type="tel"], .footer-area input[type="range"], .footer-area input[type="date"], .footer-area input[type="month"], .footer-area input[type="week"], .footer-area input[type="time"], .footer-area input[type="datetime"], .footer-area input[type="datetime-local"], .footer-area input[type="color"], .footer-area textarea,
.footer-area select{    
    border: none;
    font-size: 15px;
    height: 55px;
    /* width: 100%; */
    margin-bottom: 10px;
    padding: 5px 10px;
    font-style: normal;
    font-weight: 400;
    background: #ccc;
}
.footer-area input[type="submit"] {  
        background: none;
    border: 2px solid;
    font-size: 16px;
    font-weight: 600;
    border: none;
    padding: 0px;
    padding: 0;
    color: #FFF;
    -moz-transition: all 0.3s ease;
    border-radius: 0;
    box-shadow: none;
}
 .footer-area .widget_calendar tfoot tr td a, 
 .footer-area .s-footer .textwidget p a {
        background: none;
}
.footer-area input[type="submit"]{
    display: inline-block;
    background: #e1e1e1;
    padding: 18px 30px;
    font-size: 16px;
    font-weight: 600;
    background-color: #fff;
    color: #1d62b8;
    position: relative;
    /* text-indent: -999px; */
    border-radius: 0;
    transition: all 0.5s;
}
/*.footer-area .wpcf7:after {
    content: "\f1d8";
    position: absolute;
    font-size: 18px;
    color: #fff;
    background-repeat: no-repeat;
    font-family: 'Fontawesome';
    text-align: center;
    right: 26px;
    margin: 0;
    padding-top: 15px;
    z-index: 99;
}*/
.footer-area .widget_calendar tfoot tr td a, 
.footer-area .s-footer .textwidget p a {
    background: none;
    font-size: 13px;
    font-weight: 600;
}
 .footer-area .widget_calendar tfoot tr td a:hover,
.footer-area .s-footer .textwidget p a:hover{
    opacity: 0.6;
    background: none;
}
.footer-area div.wpcf7 input[type="file"] {font-size: 15px;font-weight: 500;width: 100%;margin-bottom: 30px;}
.footer-area select option{font-weight: 400;}

.footer-area table tr {text-align: center;line-height: 28px;}
.footer-area table td {font-size: 15px;padding: 5px;}
.footer-area .widget_calendar table thead tr th{text-align: center;}
.footer-area fieldset {
    padding-top: 0;
}
footer#footer .widget_calendar thead { background: transparent;box-shadow: 0 0px 3px 0 #88828252;}
.footer-area figure.gallery-item img:hover{opacity: 0.6;}
.footer-area p span {color: #fe900f;font-weight: 400;}


.footer-area .widget_recent_entries ul li {line-height: 25px;}
.single-footer-1 {
    margin-bottom: 2.5em;
}
.single-footer-4{text-align: left;}
.footer_area-img img {
    width: 100%;
    height: 100%;
    border-radius: 5px;
}
.footer_area-img {max-width: 80%;padding-top: 10px;}

.footer_facility-text {
    padding: 5px 2em 15px;
    background: #e7e7e7;
    border: 1px solid #d3d3d3;
    border-right: 0;
    font-size: 15px;
    color: #4c4c4c;
    text-align: center;
}
.footer-facility-area .col-md-4.pd-0:last-child .footer_facility-text{
    border-right: 1px solid #d3d3d3;
}
.footer_facility-text i.fa {
      padding: 0;
    border-radius: 0%;
    width: 35px;
    height: 27px;
    line-height: 27px;
    text-align: center;
    font-size: 27px;
    border: none;
    top: 5px;
    position: relative;
    color: #575757;
}
.footer-area .midix {
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    -webkit-animation-duration: 5s;
    animation-duration: 5s;
}
.blocks-gallery-grid li:before{display: none;}
.blocks-gallery-grid li{  float: left;}
.blocks-gallery-grid img{
    width: 100%;
    height: 80px !important;
    border-radius: 10px;
}
.single-footer-2 ,
.single-footer-3,
.single-footer-4 { margin-top: 3em;}

.newsletter{
    position: relative;
    background: #fff; 
    padding: 2em 2em 0.5em 2em;
    margin-bottom: 2em;
}
.newsletter h5{
    font-size: 26px;
    font-weight: 700;
    margin: 13px 0 0 0;
}
#innerpage-box p {margin-bottom: 24px;font-size: 14px;color: #ffffff;line-height: 24px;font-weight: 400;word-break: break-word;}
/*----------------------------------------*/
/*  09. Inner team page
/*----------------------------------------*/
#team .teaminn-page,
#innerpage-box .our-team{display: none;}
#innerpage-box .team-text h4 {
    word-break: break-word;
    color: #FE8A00;
    margin-bottom: 5px !important;
}
body.page-template-default main#innerpage-box .team-text h4:after{
    display: none;
}
#innerpage-box .team-text .team-designation{    
    word-break: break-word;
    font-size: 14px;
    margin-bottom: 10px;
    font-weight: 400;
}
#innerpage-box .team-text {
    text-align: center;
    margin: 0;
}
#innerpage-box .teaminn-page {
   /* background: linear-gradient(0deg,#4b83c9,#1d61b8 80%) no-repeat;*/
   transition: all 0.5s;
    position: relative;
    z-index: 2;
}
#team .single-team-inn,
#innerpage-box .single-team {display: none;}
#innerpage-box .single-team-inn {
    /*filter: drop-shadow(-1px -1px 4px rgba(50, 50, 0, 0.2));*/
    margin-bottom: 2em;
    overflow: hidden;
}
#innerpage-box .pd-0 {padding: 0;}
#innerpage-box .team-con {
    background: #fff;
    position: relative;
    padding: 10px 10px;
    border-radius: 10px;
    margin-top: 1px;
}
#innerpage-box .teaminn-page p {
    display: none;
}
#innerpage-box .teaminn-page .team-social-icon {
    position: absolute;
    margin: auto 20px;
    top: 50%;
    left: 0;
    right: 0;
    visibility: hidden;
    opacity: 0;
    -webkit-transform: translateY(40px);
    transform: translateY(40px);
    transition: all 0.5s;
    /*padding: 10px 0;*/
    display: inline-block;
    /*border-radius: 50px;*/
    text-align: center;
}
#innerpage-box .teaminn-page:hover .team-social-icon {
    opacity: 1;
    visibility: visible;
    transform: translateY(0px);
    -webkit-transform: translateY(0px);
    transition-delay: 0.3s;
    -webkit-transition-delay: 0.3s;
    z-index: 3;
    transition: all 0.5s;
}
#innerpage-box .teaminn-page .team-social-icon li {
    display: inline-block;
}
#innerpage-box .teaminn-page .team-social-icon li a {
    display: inline-block;
    color: #fff; transition: all 0.5s;
    width: 40px;
    height: 40px;
    line-height: 40px;
    border-radius: 50px;
}
#innerpage-box .teaminn-page .team-social-icon li a:hover {color: #111;}
#innerpage-box .teaminn-page .team-social-icon li a i{font-size: 16px;font-weight: 600;}
#innerpage-box .inner-area-title {
    margin: 0 0 5px;
}
#innerpage-box .single-team-img:after {
    content: "";
    top: 0;
    left: 0;
    border: 1px solid #c0a483;
    width: 100%;
    height: 85%;
    position: absolute;
    z-index: -1;
}
/*img */
#innerpage-box .single-team-img{
    padding: 30px 30px 0 30px;
    position:relative
}
#innerpage-box .single-team-img img {    
    width: 100%;
    height: 400px;
    border-radius: 0px;
}

/*=======================================================
===========testimonials inner pages====================
=======================================================*/
#testimonials .ts-area-single-inn,
#innerpage-box .ts-area-single{display: none;}
#innerpage-box .item.innertest-item{
    position: relative;
    overflow: hidden;
    float: left;
    margin-bottom: 1em;
    border-radius: 0 0 0px 0px;
    width: 50%;
}
#innerpage-box .ts-area-single-inn {
    margin: 1em 1em 1em 1em;
}

#innerpage-box .ts-area-c p {
    font-size: 16px;
    line-height: 25px;
    margin: 14px 0;
    display: inline-block;
    position: relative;
}
#innerpage-box .ts-area-c blockquote{
    background: none;
    box-shadow: none;
    padding: 0;
}
#innerpage-box .ts-area-c i {
    text-align: right;
    float: right;
    font-size: 24px;
    transition: all 1s ease 0s;
}
#innerpage-box .ts-area-content:hover .ts-area-c i{
    transition: all 1s ease 0s;
    transform: rotateY(180deg);
}
#innerpage-box .ts-area-bx{
    background: none !important;
    padding:10px 0px;
}
#innerpage-box .text-designation {
    font-size: 14px;
    margin-top: 6px;
    font-weight: 600;
}
#innerpage-box h3.ts-area-title {
    font-weight: 600 !important;
    position: relative;
    margin: 0px !important;
}
#innerpage-box .ts-area-content h3:after{
    display: none !important;
}
#innerpage-box .ts-area-c {
    position: relative;
    display: block;
    background: #ffffff;
    padding: 2em 2em;
    border: 1px solid #e1e1e1;
    margin-bottom: 2em;
}
#innerpage-box .ts-area-c:before {
    position: absolute;
    z-index: 0;
    content: '';
    bottom: -16px;
    left: 20px;
    border-style: solid;
    border-width: 16px 14px 0 14px;
    border-color: #9e3737 transparent transparent transparent;
}
#innerpage-box .ts-img {
    position: relative;
}
#innerpage-box .ts-area-thumb img {
    width: 75px;
    height: 75px;
    transition: all 1s ease 0s;
    border-radius: 50px;
}
#innerpage-box .ts-area-content:hover .ts-area-thumb img{
    transition: all 1s ease 0s;
    border-radius: 10px;
}
/*--------------------------------------------------------------
# Gallery page
--------------------------------------------------------------*/
#innerpage-box .lz-gallery-images {position: relative;overflow: hidden;margin-bottom: 30px;}
#innerpage-box .lz-gallery-images .box-content{
    color: #fff;
    opacity: 0;
    position: absolute;
    top: 50%;
    margin: 0 auto;
    text-align: center;
    transition: all 0.45s ease;
    left: 0;
    right: 0;
}
#innerpage-box .lz-gallery-images:hover .box-content{ opacity: 1; }
#innerpage-box .spa-gall {
  display:inline-block;
  overflow: hidden;
  width: 100%;
  box-shadow: none !important;
  padding: 15px;
  position: relative;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
}
#innerpage-box .spa-gall img {
    position: relative;
    transition: 0.5s all;
    width: 100%;
    height: 250px;
    z-index: 1;
}
#innerpage-box .spa-gall:before,
#innerpage-box .spa-gall:after {
    background-color: #444;
    z-index: 0;
    position: absolute;
    content: '';
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
}
#innerpage-box .spa-gall:before {

    top: 0px;
    left: 0px;
    bottom: 210px;
    border-radius: 0 29px;
    right: 328px;
    -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
}
#innerpage-box .spa-gall:after {
  bottom: 0px;
  right: 0px;
  top: 210px;
  left: 328px;
  border-radius: 0 29px;
  -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
}

#innerpage-box .spa-gall:hover:before,
#innerpage-box .spa-gall:hover:after{
    background: #3ff;
    transition: all 0.4s ease;
}
#innerpage-box .spa-gall:hover:before{

  top: 0px;
  left: 0px;
  bottom: 50px;
  right: 50px;
  -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
}
#innerpage-box .spa-gall:hover:after {
  bottom: 0px;
  right: 0px;
  top: 50px;
  left: 50px;
  -webkit-transition: all 0.4s ease;
    transition: all 0.4s ease;
}
/*----------------------------------------*/
/* project inner Page
/*----------------------------------------*/

#innerpage-box .single-project-bx{
    margin-bottom: 25px;
    padding: 0 15px;
}
#innerpage-box .single-project{
    position: relative;
    overflow: hidden;
    transition: all .8s;
    text-align: center;
    /*border-radius: 15px;
    box-shadow:0 3px 10px 2px rgb(63 78 100 / 15%);*/
}
#innerpage-box .single-project-bx .right {
    padding: 10px 10px;
    position: absolute;
    width: 100%;
    bottom: 8px;
}
#innerpage-box .single-project-bx .project-link {
    bottom: 0;
    left: 0;
    position: absolute;
    right: 0;
    text-align: center;
    top: 0;
}
#innerpage-box .single-project-bx .project-link i{
    display: inline-block;
    font-size: 16px;
    width: 50px;
    height: 50px;
    line-height: 50px;
    opacity: 0;
    position: relative;
    text-align: center;
    top: 00px;
    transition: all .5s;
    z-index: 5;
    background: #f3f;
    color: #000;
}
#innerpage-box .single-project-bx:hover .project-link i{
    transition: all .5s;
    opacity: 1;
    top: 100px;
    display: inline-block;
    transform: rotateZ(360deg);
}
#innerpage-box .single-project-bx .project-link i:hover{
    transform: rotateY(180deg);
    transition: all .5s;
}

#innerpage-box .single-project h4.inner-area-title{
    position: relative;
    display: inline-block;
    word-break: break-word;
    font-weight: 600 !important;
    transition: all .8s;
    margin: 0px 0!important; 
     z-index: 1;
}

#innerpage-box .single-project-bx .right p{
    display: none;
}
#innerpage-box .single-project .btn5 {
 display: none;
}
#innerpage-box .project-img {position: relative;overflow: hidden;}
#innerpage-box .project-img img{
    width: 100%;
    height: 250px;
    border-bottom: 8px solid #3ff;
}

/*--------------------------------------------------------------
## Services inner page
--------------------------------------------------------------*/
body.page-template-default main#innerpage-box .inner-area-title:after {display: none;}

body.page-template-default main#innerpage-box .single-service-bx .inner-area-title, 
body.page-template-default main#innerpage-box .single-service-bx .inner-area-title small{
    font-weight: 600;
    margin-bottom: 10px;
}

.service-area .servicesinn,
#innerpage-box .single-service-bx {display: none;}

#innerpage-box .inser{width: 33.33%;float: left;}
#innerpage-box .servicesinn {  margin: 0 15px;}
#innerpage-box .single-service {  margin-bottom: 30px;transition: all 1s ease 0s;}
#innerpage-box .service-title-box p {margin: 15px 0;}

#innerpage-box .service-title-box .inner-area-title{ 
    padding-top: 0em;
    color: #121938;
    margin-bottom: 0;
    font-weight: 700;
 }
#innerpage-box .service-icon img {
    transform: scale(1);
    transition: all 5s ease 0s;
    width: 100%;
    height: 270px;
}
#innerpage-box .service-title-box {
    transition: all 5s ease 0s;
    padding: 10px 20px;
    box-shadow: 0 3px 8px rgb(52 53 55 / 10%);
    position: relative;
    overflow: hidden;
    text-align: center;
    background: #f3f;
    margin: 0px auto;
    transition: all 1s ease 0s;
}
#innerpage-box .single-service:hover .service-title-box{
    transition: all 1s ease 0s;
    box-shadow: 0 3px 18px rgb(52 53 55 / 60%);
}

#innerpage-box .single-service .service-icon{text-align: center;position: relative;overflow: hidden;}
#innerpage-box .service-icon .ovrly{
    border-radius: 500px;
    /*background: linear-gradient(to top,transparent 60%,#ff5317 75%);*/
    background: #3ff;
    opacity: 0.3;
    height: 100%;
    position: absolute;
    width: 100%;
    right: 0%;
    top: 50%;
    -webkit-transform: scale(0);
    -moz-transform: scale(0);
    -ms-transform: scale(0);
    -o-transform: scale(0);
    transform: scale(0);
    -webkit-transition: all 0.5s;
    -moz-transition: all 0.5s;
    -o-transition: all 0.5s;
    transition: all 0.5s;
}

#innerpage-box .service-icon img{width: 100%;height: 280px;transition: all 0.5s ease 0s;}
#innerpage-box .servicesinn .btn5{ margin-top: 25px;}
#innerpage-box .servicesinn .btn5 a {
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-decoration: none;
    font-weight: 600;
    font-size: 16px;
    padding: 1em 2em;
    color: #fff;
    transition: all 0.5s;
    border-radius: 5px;
    cursor: pointer;
    text-align: center;
    z-index: 9;
    max-width: 12em;
   word-break: break-all;
    background-color: #89d8d3;
    border: none;
}
#innerpage-box .servicesinn .btn5 a:after {
  position: absolute;
  content: "";
  width: 100%;
  height: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  border-radius: 5px;
   background-color: #4dccc6;
/*background-image: linear-gradient(315deg, #4dccc6 0%, #96e4df 74%);*/
  transition: all 0.3s ease;
}
#innerpage-box .servicesinn .btn5 a:hover {
  color: #fff;
}
#innerpage-box .servicesinn .btn5 a:hover:after {
  top: 0;
  height: 100%;
}
#innerpage-box .servicesinn .btn5 a:active {
  top: 2px;
}

/*-----------------------
## Site map Template 
-----------------------------*/

div#sitemap-box {
    border-top: 2px solid #f94c34;
    border-radius: 0;
}
div#sitemap-box:before {
       position: absolute;
    content: "";
    display: inline-block;
    width: 35px;
    border-width: 2px;
    border-style: solid;
    border-color: #c9593f;
    right: 0;
    top: 16px;
    left: 0%;
    margin: 0 auto;
    border-top: none;
    border-right: none;
    transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -webkit-transform: rotate(90deg);
    z-index: 1;
}

div#sitemap-box h3 {
      font-size: 20px;
    background: #2e2e2e;
    padding: 15px 25px;
    color: #ffffff;
    border-radius: 25px 0;
    text-align: center;
    word-break: break-word;
    box-shadow: 0px 6px 10px -4px rgba(0,0,0,0.4);
    font-weight: 500;
}

.page-template-templates main#innerpage-box div#sitemap-box h3:after {
    display: none;
}
main#innerpage-box div#sitemap-box ul li {
    list-style: none;
    padding-bottom: 5px;
}
main#innerpage-box div#sitemap-box ul li a {
    position: relative;
    padding-left: 20px;
}
main#innerpage-box div#sitemap-box ul li a:before {
    content: "\f111";
    font-family: 'Fontawesome';
    margin-right: 10px;
    color: #c9593f;
    font-size: 10px;
    display: inline-block;
    transform: scale(1);
    transition: all 0.5s ease 0s;
}
main#innerpage-box div#sitemap-box ul li.current_page_item a {
    background: none;
}
main#innerpage-box div#sitemap-box ul li.current_page_item a:before{
    z-index: 1;
}
main#innerpage-box div#sitemap-box ul li a:hover:before {
    transform: scale(1.4);
}
div#sitemap-box ul {
    margin: 0;
}

div#sitemap-box .sitemap-blogposts ul {
    padding: 5px 5px 5px 0;
    margin: 0;
}
div#sitemap-box .sitemap-posts-box {
    margin-bottom: 15px;
}
div#sitemap-box .blogpostsitemap li {
    list-style: none;
}
div#sitemap-box .sitemap-posts-box img {
    width: 100%;
}
div#sitemap-box h3:before {
      position: absolute;
    content: "";
    display: inline-block;
    width: 20px;
    border-width: 2px;
    border-style: solid;
    border-color: #f94c34;
    right: 0;
    top: 9px;
    left: 0%;
    margin: 0 auto;
    border-top: none;
    border-right: none;
    transform: rotate(45deg);
    -ms-transform: rotate(45deg);
    -webkit-transform: rotate(90deg);
    z-index: 1;
}

div#sitemap-box ul {
    margin: 0;
    list-style: none;
    padding: 0;
    margin: 0 0 0px 20px;
}
/*-----------------------
## End Site map Template 
-----------------------------*/
/*-----------------------
## Shop page
-----------------------------*/
.archive .ht-main-title {
    margin: 0em 0 0.5em;
    padding: 1em 0 0;
}

/*-----------------------
## Contact Template
-----------------------------*/

ul.contact-sm-links {list-style: none;margin: 2em auto 0;}
ul.contact-sm-links li {padding: 7px 0;display: inline-block;}

main#innerpage-box #ht-contactus-wrap .social_area h2 {
    font-size: 60px;
    font-weight: 600;
}
.map_area_label {
    text-align: center;
    padding: 6em 12em;
    padding: 6em 12em 2em;
}

main#innerpage-box #ht-contactus-wrap .map_area_label h4{
    font-size: 33px;
    color: #000;
    letter-spacing: -1px;
    line-height: 45px;
}
.contact-mapbox {width: 100%;}
.contact-mapbox iframe {
    z-index: 1;
    width: 100%;
    height: 450px;
}
#ht-contactus-wrap .contact-page-form {
    text-align: left;
    border-style: solid;
    border-width: 6px 6px 6px 6px;
    border-color: #F4F4F4;
    padding:1em 1em 2em 1em;
    border-radius: 6px;
    margin-top: 2em;
}

#ht-contactus-wrap input[type="text"], 
#ht-contactus-wrap input[type="email"], 
#ht-contactus-wrap input[type="url"],
#ht-contactus-wrap input[type="password"], 
#ht-contactus-wrap input[type="search"], 
#ht-contactus-wrap input[type="number"], 
#ht-contactus-wrap input[type="tel"], 
#ht-contactus-wrap input[type="range"], 
#ht-contactus-wrap input[type="date"], 
#ht-contactus-wrap input[type="month"], 
#ht-contactus-wrap input[type="week"], 
#ht-contactus-wrap input[type="time"], 
#ht-contactus-wrap input[type="datetime"], 
#ht-contactus-wrap input[type="datetime-local"], 
#ht-contactus-wrap input[type="color"], 
#ht-contactus-wrap select, 
#ht-contactus-wrap textarea {
    margin-bottom: 15px;
    border: none; 
    /*border: 2px solid;*/
    font-size: 15px;
    padding: 18px 20px;
    border-radius: 6px;
    /*background-color: transparent;*/
}

#ht-contactus-wrap .address-c-box{
    position: relative;
    margin: 2em 0;
    z-index: 2;
}

#ht-contactus-wrap input[type="file"] {
    font-size: 14px;
}
#ht-contactus-wrap textarea {
    height: 150px;
}
#ht-contactus-wrap label {
    font-weight: 400;
        width: 100%;
}
#ht-contactus-wrap select {
    width: 100%;
}
#ht-contactus-wrap input[type="checkbox"], #ht-contactus-wrap input[type="radio"], #ht-contactus-wrap input[type="file"] {
    margin-bottom: 15px;
}
#ht-contactus-wrap input[type=date] {
    /*line-height: 10px;*/
    padding: 8px 8px;
}
main#innerpage-box #ht-contactus-wrap h2{
    color: #333333;
    font-size: 45px;
    font-weight: 500;
}
#ht-contactus-wrap input[type="submit"] {
    padding: 15px 34px;
    margin-left: 10px;
    display: inline-block;
    vertical-align: middle;
    -webkit-transform: perspective(1px) translateZ(0);
    transform: perspective(1px) translateZ(0);
    box-shadow: 0 0 1px rgb(0 0 0 / 0%);
    -webkit-transition-duration: 0.3s;
    transition-duration: 0.3s;
    -webkit-transition-property: transform;
    transition-property: transform;
}
.pd-8 {
    padding: 0 8px;
}
.mg-3 {
    margin: 3em 0 0;
}
.pd-r {
     padding-right: 2em;
}
.pd-l {
    padding: 4em 0;
    padding-left: 4em;
    text-align: left;
}
.page-template-contact-template .inner_contentbox {
    padding: 0em 0em 0;
}
.page-template-contact-template .innerpage-whitebox {
    padding: 0 0em;
}
#ht-contactus-wrap .con-inn-img {   margin-top: 9em;}
/*#ht-contactus-wrap .con-inn-img img{height: 350px;}*/
#ht-contactus-wrap .Address_area{
    background: none;
    /*padding: 0em 2em;*/
    margin-bottom: 3em;
    margin-right: 0em;
    margin-top: 3em;
    /*box-shadow: 1px 2px 10px 11px rgb(65 49 49 / 10%);*/
}
.page-template-contact-template  main#innerpage-box .Address_area h4, 
.page-template-contact-template  main#innerpage-box .social_area h4  {
    font-size: 50px;
    color: #000;
    font-weight: 800;
    margin-bottom: 0;
}

.page-template-contact-template main#innerpage-box .Address_area h4:after,
.page-template-contact-template main#innerpage-box .social_area h4:after {
    display: none;
 }

#innerpage-box .contsec img {
    width: 100%;
    height: 550px;
}
main#innerpage-box .Contact_area_text {
    font-size: 16px;
    color: #777777;
    padding: 0 10px;
}
main#innerpage-box .social_area h4 {
    color: #fff;
}
main#innerpage-box .social_area .Contact_area_text {
    color: #fff;
}

#ht-contactus-wrap .Address_area i {
    font-size: 45px;
    color: #c9593f;
}
#ht-contactus-wrap .contact_l_area {
    width: 65px;
    height: 65px;
    line-height: 65px;
    /*border: 1px solid #000;*/
    border-radius: 50px;
    text-align: center;
    padding: 0;
    margin: 0 auto;
    margin-top: -4em;
    position: relative;
}
#ht-contactus-wrap .contact_l_area i {
    font-size: 30px;
    transition: all 0.4s;
    top: 4px;
    position: relative;
}
#ht-contactus-wrap .contact-label {
    font-size: 20px;
    margin-bottom: 10px;
    font-weight: 600;
    margin-top: 15px;
}
#ht-contactus-wrap .contact-info {
    margin-top: 0px;
    color: #bebebe;
    font-size: 15px;
}
#ht-contactus-wrap .contact_area {
    margin-bottom: 0em;
    border-bottom: 3px solid #000;
    padding: 2em 1em;
    transition: all 0.4s;
    text-align: center;
}
#ht-contactus-wrap .contact_area:hover .contact_l_area i{
    transform: rotateY(180deg);
    transition: all 0.4s;
}
#ht-contactus-wrap .contact-info a{
    color: #bebebe;
}
#ht-contactus-wrap .contact-info p{
    margin: 0;
}
#ht-contactus-wrap .contactus-img img {
    width: 100%;
    height: 100%;
    border-radius: 0 10px 0 0;
}
#ht-contactus-wrap .tada {
    -webkit-animation-name: tada;
    animation-name: tada;
}
#ht-contactus-wrap .midix {
    -webkit-animation-fill-mode: both;
    animation-fill-mode: both;
    -webkit-animation-timing-function: linear;
    animation-timing-function: linear;
    -webkit-animation-duration: 5s;
    animation-duration: 5s;
}

/*==== for slider bouncing arrow =========*/

@-moz-keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    -moz-transform: translateY(0);
    transform: translateY(0);
}
40% {
    -moz-transform: translateY(-30px);
    transform: translateY(-30px);
}
60% {
    -moz-transform: translateY(-15px);
    transform: translateY(-15px);
}
}
@-webkit-keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    -webkit-transform: translateY(0);
    transform: translateY(0);
}
40% {
    -webkit-transform: translateY(-30px);
    transform: translateY(-30px);
}
60% {
    -webkit-transform: translateY(-15px);
    transform: translateY(-15px);
}
}
@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    -moz-transform: translateY(0);
    -ms-transform: translateY(0);
    -webkit-transform: translateY(0);
    transform: translateY(0);
}
40% {
    -moz-transform: translateY(-30px);
    -ms-transform: translateY(-30px);
    -webkit-transform: translateY(-30px);
    transform: translateY(-30px);
}
60% {
    -moz-transform: translateY(-15px);
    -ms-transform: translateY(-15px);
    -webkit-transform: translateY(-15px);
    transform: translateY(-15px);
}
}
.bounce {
  -moz-animation: bounce 2s infinite;
  -webkit-animation: bounce 2s infinite;
  animation: bounce 2s infinite;
}
/*==== for bouncing arrow =========*/
.social_area {
    position: relative;
    padding: 0 2em;
}
.social_area i.fa{
      font-size: 45px;
    color: #fff;
    z-index: 999;
    cursor: pointer;
    -moz-transform: rotate(270deg);
    -webkit-transform: rotate(270deg);
    -o-transform: rotate(270deg);
    -ms-transform: rotate(270deg);
    transform: rotate(270deg);
    position: absolute;
    top: 60px;
    right: 50%;
    font-size: 43px;
}

/*-----------------------
## End Contact Template
-----------------------------*/

/*-----------------------
## FAQS page
-----------------------------*/
.faq_tp {text-align: center;width: 100%;}
.faq_bm h4:after,.faq_tp h4:after,.faq_tp h1:after{display: none !important;}
.faq_bm h4:before,.faq_tp h4:before {
    top: 0;
    left: -12px;
    width: 30px;
    position: relative;
    content: "";
    display: inline-block;
    border-width: 3px;
    border-style: solid;
    border-left: none;
    border-top: none;
    border-right: none;
    border-color: #01beff;
    -ms-transform: translateY(-50%);
}
.faq_bm h4,.faq_tp h4{font-weight: 700 !important ;}
.faq_bm .faq-heading,.faq_tp .faq-heading{
    font-size: 60px;
    font-weight: 700;
    letter-spacing: 7px;
    max-width: 675px;
    margin: 0 auto;
    padding-bottom: 1.5em;
}
.faq_bm .faq-heading{padding-bottom: 0 !important;}
#content-box .faqlast{
    padding-top: 5em;
}
#content-box .faq-form input[type="text"], 
#content-box .faq-form input[type="email"], 
#content-box .faq-form input[type="url"],
#content-box .faq-form input[type="password"], 
#content-box .faq-form input[type="search"], 
#content-box .faq-form input[type="number"], 
#content-box .faq-form input[type="tel"], 
#content-box .faq-form input[type="range"], 
#content-box .faq-form input[type="date"], 
#content-box .faq-form input[type="month"], 
#content-box .faq-form input[type="week"], 
#content-box .faq-form input[type="time"], 
#content-box .faq-form input[type="datetime"], 
#content-box .faq-form input[type="datetime-local"], 
#content-box .faq-form input[type="color"], 
#content-box .faq-form select, 
#content-box .faq-form textarea {
    margin-bottom: 15px;
    border: none; 
    /*border: 2px solid;*/
    font-size: 15px;
    padding: 18px 20px;
    border-radius: 0px;
    background-color: #f3f3;
}
#content-box .faq-form textarea {height: 100px;}
#content-box .faq-form input[type="submit"]{
    font-size: 16px;
    padding: 15px 40px;
    border-radius: 0;
    font-weight: 600;
}
#content-box .faqimg {
    padding-top: 45px;
}
#content-box .faqimg img{
    height: 550px;
    z-index: 2;
    position: relative;
    left:-50px;
}
#content-box .faqimg:after {
    border: 4px solid #ff6a01;
    content: '';
    position: absolute;
    z-index: 0;
    right: 30px;
    top: 0px;
    bottom: 60px;
    left: 60px;
}
div#content-box .faq-content button.accordion {
    color: #444;
    cursor: pointer;
    width: 100%;
    text-align: left;
    outline: none;
    transition: 0.4s;
    margin: 0;
    background: #fbb34d;
    border-radius: 0px ;
}
div#content-box .faq-content button.accordion.active, button.accordion:hover {
    opacity: 1;
}
div#content-box .faq-content button.accordion:before {
    content: "\f067";
    font-family: 'Fontawesome';
    font-size: 16px;
    color: #000;
    float: right;
    position: absolute;
    top: 0px;
    font-weight: normal;
    line-height: 45px;
}
div#content-box .faq-content button.accordion.active:before {
    content: "\f068";
    font-family: 'Fontawesome';
}
div#content-box .faq-content div.panel {
    max-height: 0;
    overflow: hidden;
    transition: 0.6s ease-in-out;
    opacity: 0;
    margin: 5px 0 10px 0;
    border-radius:0 ;
    background: linear-gradient(0deg,#8972ea,#516ced 80%) no-repeat;
    border: none;
    padding: 10px 60px 0;
    /*box-shadow: 1px 0px 6px 4px rgb(63 78 100 / 15%);*/
}
div#content-box .faq-content div.panel p{
    margin:0 0 10px;
}
div#content-box .faq-content div.panel.show {
    opacity: 1;
    max-height: 500px;  
}
main#innerpage-box div#content-box h3.faq-title {
   padding-left: 35px;
    margin: 0;
    font-weight: 600;
    line-height: 25px;
    word-break: break-word;
    text-align: left;
    font-size: 18px;
}
main#innerpage-box div#content-box h3.faq-title:after {
    display: none;
}
div#content-box .faq-content.padding0 p {
    font-size: 15px;
    padding: 25px 25px 10px 25px;
    letter-spacing: 1px;
    word-break: break-word;
}
/*-----------------------
## End FAQS page
-----------------------------*/
/*===========*/
.col-container {
    display: table;
    width: 100%;
}
.col-facility{
    display: table-cell;
}
.heading.active i {
    box-shadow: 0px 0px 0px 2px #FFF, 0px 0px 0px 4px #0158a6;
}
.test-right img {
    width: 100%;
    height: 100%;
}
.heading{cursor: pointer;}
@media screen and (min-width: 769px){
    .inside-full-height{height:100%}

}
/* Catalogue slider Start */
.slider {width: 86%;position: relative;height: 490px;margin: auto;overflow-x: scroll;overflow-y: hidden;}
.slider::-webkit-scrollbar {display: none;}
.slider .slides { display: flex;position: absolute;left: 0;transition: 0.3s left ease-in-out;}
.slider .slide-item { margin-right: 35px;width: 334px;margin-left: 0;}
.slider .slide-item:last-child {margin-right: 0;}

.ctrl {text-align: center;margin-top: 5px;}
.ctrl-btn {font-size: 13px !important; background: none;border: none;font-weight: 600;text-align: center;cursor: pointer;outline: none;color: #9e9e9e;position: absolute;margin-top: -60.5px;height: 23px;bottom: 0;}
.ctrl-btn:hover{color:#000;background: none;}
.ctrl-btn.pro-next {right: 45%;position: absolute;border: 2px solid #bfbfbf; padding: 3px 5px 3px 5px;margin: 0;z-index: 999;height: 45px;width: 45px;}
.ctrl-btn.pro-prev {left: 46%;position: absolute;border: 2px solid #bfbfbf;padding: 0;margin: 0; z-index: 999;height: 45px;width: 45px;}
/*--------------------------------------------------------------
## Add to card
--------------------------------------------------------------*/
.total-count a.button.wc-forward { background: none; border: none; position: relative; right: 19px;}
.total-count a.button.wc-forward:hover { background: none; border:none;}
.total-count .count {    
        color: #1d62b8;
    position: absolute;
    /* padding: 2px; */
    z-index: 300;
    top: 6px;
    right: 20px;
    font-size: 8px;
    border-radius: 20px;
    width: 12px;
    height: 12px;
    line-height: 12px;
    text-align: center;
    background: #ffe723;
}
.total-count img {    
    width: auto;
    height: auto;
    margin: 0px 0 0;
}
.total-count {
    text-align: right;
}
.total-count i {
    font-size: 20px;
    border: 1px solid #fff;
    padding: 10px;
    color: #fff;
    background: transparent;
        -webkit-transition: transform 0.5s ease-out;
    transition: transform 0.5s ease-out;
}
.total-count a:hover i{
    transform: translateY(-5px);
}
/*--------------------------------------------------------------
## carousel slider
--------------------------------------------------------------*/
.carousel-inner {
    border-radius: 10px;
}
.quote-box:after{
    content: "";
    background-image: url(images/quote-boxbg.png);
    width: 28px;
    height: 23px;
    position: absolute;
    background-repeat: no-repeat;
    bottom: -23px;
    left: 10%;    
}
.quote-box{
	border: 2px solid #ffffff;
    position: relative;    
    padding:20px;   
}

#quote-carousel {
    padding: 0 10px 30px 10px;
    clear: both;
    /*margin-top: 30px;*/
}

#quote-carousel .carousel-control {
    background: none;
    color: #fc856d;
    font-size: 1.3em;
    text-shadow: none;
    margin-top: 225px;
    opacity: 1;
    z-index: 2000;
}
#quote-carousel .carousel-control.left {
    left: -35px;
    top: -60px;
}
#quote-carousel .carousel-control.left i {
    color: #fff;
    background: #c6c6c6;
    border-radius: 50%;
    padding: 5px 12px;
    opacity: 1;
    display: none;
}
#quote-carousel .carousel-control.right {
    right: -35px;
    top: -60px;
}
#quote-carousel .carousel-control.right i {
    color: #fff;
    background: #c6c6c6;
    border-radius: 50%;
    padding: 5px 12px;
    opacity: 1;
    display: none;
}
#quote-carousel .carousel-control.left i:hover {
    color: #ea70a1;
}
#quote-carousel .carousel-control.right i :hover {
    color: #ea70a1;
}
#quote-carousel .carousel-indicators {
    right: 50%;
    top: auto;
    bottom: -50px;
    margin-right: -19px;
}
#quote-carousel .carousel-indicators li {
    width: 10px;
    height: 10px;
    cursor: pointer;
    border-radius: 50px;
    overflow: hidden;
    transition: all 0.4s;
    margin-right: 8px;
}
#quote-carousel .carousel-indicators .active {
    background: #ffffff;
    width: 10px;
    height: 10px;
    border-color: #fff;
    opacity: 1;
    overflow: hidden;
}
.carousel-inner {
    min-height: 170px;
    border-radius: 10px;
        position: relative;
    width: 100%;
    overflow: hidden;
    top: -48px;
    z-index: 1;
}
.item blockquote {
    border-left: none;
    margin: 0;
}
/*.item blockquote p:before {
    content: "\f10d";
    font-family: 'Fontawesome';
    float: left;
    margin-right: 10px;
}*/
/*--------------------------------------------------------------
## Posts and pages
--------------------------------------------------------------*/
.home-page {
    padding: 0 0 30px 0;
}
#innerpage-box{
    position: relative;
    padding:0;
    background: #ffffff;
}
.single-post .ht-main-title {
    margin: 0px 0 15px;
    padding: 15px 15px 0;
}
.innerpage-whitebox{
     padding: 0 1em;
    border-radius: 10px;
    margin-bottom: 10px;
}
#content-box h4{
    margin: 20px 0 5px 0; 
}
#content-box ul{
    padding: 0 0 20px 0;
    list-style: none;
    font-size: 18px;
    margin: 0;
}
#content-box ul li:before {
       content: "\f111";
    font-family: 'Fontawesome';
    margin-right: 10px;
    transform: scale(1);
    transition: all 0.5s ease 0s;
    color: #fff;
    display: inline-block;
    transform: scale(1);
    transition: all 0.5s ease 0s;
}
#content-box ul li:hover:before {
    transform: scale(1.4);
}

.widget-area ul li:before {
    content:"\f111";
    font-family: 'Fontawesome';
    margin-right: 10px;
    color: #c9593f;
    font-size: 10px;
     display: inline-block;
    transform: scale(1);
    transition: all 0.5s ease 0s;
    display: none;
}
.widget-area ul li:hover:before {
    transform: scale(1.4);
}
#content-box ol li:before {
          transform: scale(1);
    transition: all 0.5s ease 0s;
}
#content-box ol li:hover:before {
    transform: scale(1.2);
}

#content-box ul li {
    margin: 15px 0;
    line-height: normal;
}
main#innerpage-box #content-box ol {
    margin: 0;
}
#content-box ol {
    counter-reset: li;
    list-style: none;
    *list-style: decimal;
}

#content-box ol li {
    position: relative;
    padding-left: 60px;
    min-height: 60px;
    padding-top: 10px;
}
#content-box ol li:before {
    content: counter(li);
    counter-increment: li;
    color: #fff;
    background: #f07100;
    border-radius: 50%;
    font-size: 18px;
    width: 48px;
    height: 48px;
    line-height: 48px;
    text-align: center;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    box-shadow: 0 10px 30px 0 #88828230;
}
.page_featured {
      margin: 0 4em;
    margin-bottom: 2em;
    padding: 10em 4em;
}
img.ht-page-header-img{
    width: 100%;
    box-shadow: 0px 6px 10px -4px rgba(0,0,0,0.4);
}

.sticky {
    display: block;
}
.total-hentry {
    margin: 0 0 50px;
}
.byline,
.updated:not(.published) {
    display: none;
}

.page-content,
.entry-content,
.entry-summary {

}
.page-links {
    clear: both;
    margin: 0 0 15px;
}
.ht_no_sidebar #primary{
    width: auto;
    float: none;
}
.ht_no_sidebar_condensed #primary{
    width: 76%;
    float: none;
    margin: 0 auto;
}
.ht_left_sidebar #primary{
    float: right;
}
.ht_left_sidebar #secondary{
    float: left;
}
.ht-post-info{
    float: left;
    width: 80px;
    text-align: right;
    font-family: 'Oswald', sans-serif;
}
.single .ht-post-info{
    margin-right: 30px;
    margin-bottom: 20px;
}
.ht-post-info .entry-date{
    display: block;
    font-size: 0.9em;
    margin-bottom: 20px;
}
.ht-post-info .entry-date span{
    display: block;
    text-transform: uppercase;
}
.ht-post-info .entry-date span.ht-day{
    font-size: 60px;
    line-height: 1.3;
    color: #fe5722;
}
.ht-post-info a{
    display: block;
    text-decoration: none;
    line-height: 1.4;
    padding: 20px 0 0;
    text-transform: uppercase;
    border-top: 1px solid #EEE;
    font-size: 12px;
    color: #444;
    font-weight: 300;
}
.ht-post-wrapper{
    padding-left: 105px;
}
.entry-figure{
    background: #f9f9f9;
    text-align: center;
    margin-bottom: 15px;
}
.entry-header .entry-title{
    font-weight: 300;
    letter-spacing: 1px;
    margin-bottom: 10px;
}
.entry-figure + .entry-header{
    background: #FFF;
    position: relative;
    margin-top: -65px;
    width: 90%;
    padding: 15px 0 1px;
}
.entry-header .entry-title a{
    text-decoration: none;
    color: #333;
}
.entry-categories{
    font-size: 14px;
    color: #666;
    font-style: italic;
    margin-bottom: 15px;
}
.entry-categories .fa{
    color: #fe5722;
    margin-right: 8px;
}
.entry-categories a{
    color: #666;
    text-decoration: none; 
}
.entry-readmore{
    margin-top: 20px;
}
.entry-readmore a{
    background: #fe5722;
    padding: 0 20px;
    line-height: 40px;
    color: #FFF !important;
    border: 0;
    -moz-transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    display: inline-block;
}
.entry-readmore a:hover{
    background: #333;
    color: #FFF;
}
section ul{
    padding: 0 0 20px 0;
}
/*--------------------------------------------------------------
## 404 Page
--------------------------------------------------------------*/
.error404 .error-404{
    display: block;
    text-align: center;
    font-size: 200px;
    color: #f9f9f9;
    line-height: 1.2;
    font-family: josefin sans,sans-serif;
    color: #222;
    font-size: 220px;
    letter-spacing: 10px;
    margin: 0;
    font-weight: 700;
    text-shadow: 2px 2px 0 #c9c9c9, -2px -2px 0 #c9c9c9;
}
.error404 .oops-text{
    text-align: center;
}
.toggle-bar{
    display: none;
}
.error404 .error-404{
    color: #ffffff;
    font-size: 195px;
    text-align: center;
    display: block;
    animation: effect linear 1900ms infinite;
    font-weight: 600;
    margin-bottom: 15px;
    line-height: 240px;
}
@keyframes effect {
   0%{
        text-shadow:
            4px -4px 0 #000, 3px -3px 0 #000,
            2px -2px 0 #000, 1px -1px 0 #000,
            -4px 4px 0 #000, -3px 3px 0 #000,
            -2px 2px 0 #000, -1px 1px 0 #000;
    }
    25%{
        text-shadow:
            -4px -4px 0 #000, -3px -3px 0 #000,
            -2px -2px 0 #000, -1px -1px 0 #000,
            4px 4px 0 #000, 3px 3px 0 #000,
            2px 2px 0 #000, 1px 1px 0 #000;
    }
    50%{
        text-shadow:
            -4px 4px 0 #000, -3px 3px 0 #000,
            -2px 2px 0 #000, -1px 1px 0 #000,
            4px -4px 0 #000, 3px -3px 0 #000,
            2px -2px 0 #000, 1px -1px 0 #000;
    }
    75%{
        text-shadow:
            4px 4px 0 #000, 3px 3px 0 #000,
            2px 2px 0 #000, 1px 1px 0 #000,
            -4px -4px 0 #000, -3px -3px 0 #000,
            -2px -2px 0 #000, -1px -1px 0 #000;
    }
    100% {
        text-shadow:
            4px -4px 0 #000, 3px -3px 0 #000,
            2px -2px 0 #000, 1px -1px 0 #000,
            -4px 4px 0 #000, -3px 3px 0 #000,
            -2px 2px 0 #000, -1px 1px 0 #000;
    }
}

@media only screen and (max-width: 767px){
  .error404 .error-404{ font-size: 150px; }
}
@media only screen and (max-width: 479px){
   .error404 .error-404{ font-size: 150px; }
}
@media only screen and (max-width: 359px){
   .error404 .error-404{ font-size: 122px; }
}
@media screen and (min-width: 769px){
    .row-eq-height {
        display: -webkit-box;
        display: -webkit-flex;
        display: -ms-flexbox;
        display: flex;
    }
}
.woocommerce #reviews h2 small{
    float: none;
}
.woocommerce ul.products li.product .price .amount {
    font-size: 24px;
    color: #1863b6;
    font-weight: 400;
}
.woocommerce ul.products li.product .price del .amount {
    font-size: 14px;
    font-weight: 400;
}
/*-----------------------
## page scroll
-----------------------------*/

#back2Top {
    width: 43px;
    overflow: hidden;
    z-index: 999;
    /*display: none;*/
    cursor: pointer;
    -moz-transform: rotate(270deg);
    -webkit-transform: rotate(270deg);
    -o-transform: rotate(270deg);
    -ms-transform: rotate(270deg);
    transform: rotate(270deg);
    position: fixed;
    bottom: 13px;
    right: 30px;
    color: #fff;
    text-align: center;
    font-size: 30px;
    text-decoration: none;
    border-radius: 4px;
    background: transparent;
    height: 43px;
    opacity: 1 !important;
}
#back2Top:hover {
    background-color: transparent;
    color: #fff;
}
#innerpage-box p {
    font-size: 15px;
}
.widget-area ul ul {
        margin-top: 5px;
    list-style: none;
    margin: 0 0 1em;
    padding: 0;
    position: relative;
    background: none !important;
    box-shadow: none !important;
    display: block;
    z-index: 1;
}
main#innerpage-box li {
    font-size: 15px;
}
.sidebar_list {
    margin-top: 1em;
    overflow: hidden;
}
div#secondary caption {
    padding-top: 12px;
    padding-bottom: 12px;
}
div#secondary .widget_calendar table {
    margin: 0;
    padding: 0;
}
.widget_calendar tfoot tr td a {
    border: none;
    padding: 0px;
    padding: 6px 20px;
    -moz-transition: all 0.3s ease;
    border-radius: 5px;
    box-shadow: 0px 6px 10px -4px rgba(0,0,0,0.4);
}
.widget-area .widget_rating_filter ul {
    list-style: none;
    padding: 12px;
    margin: 0;
    border: 1px solid #eaeaea;
}
.widget-area .widget_rating_filter ul li {
    border-bottom: none;
}
.widget-area .woocommerce ul.cart_list li, .widget-area .woocommerce ul.product_list_widget li {
    padding: 12px 12px 12px;
}
.widget-area .woocommerce ul.product_list_widget li a {
    font-size: 13px;
    font-weight: 400;
    text-transform: capitalize;
    padding: 0px 0 8px;
}

.widget-area .woocommerce ul.cart_list li img, .widget-area .woocommerce-page ul.cart_list li img, .widget-area .woocommerce ul.product_list_widget li img, .widget-area .woocommerce-page ul.product_list_widget li img {
    width: 74px;
    height: 74px;
}
.widget-area span.woocommerce-Price-amount.amount {
    margin: 0 0 10px 0;
    line-height: 24px;
    min-height: 26px;
    color: #242424;
    font-size: 17px;
    font-weight: 500;
}
.widget-area del span.woocommerce-Price-amount.amount {
    font-size: 14px;
    color: #666;
}
.widget-area .woocommerce ul.product_list_widget li img:hover {
    opacity: 0.8;
}
.widget-area .woocommerce .widget_shopping_cart_content p {
    padding: 12px;
    margin: 0;
    border: 1px solid #eaeaea;
    margin-top: 15px;
}
.woocommerce .woocommerce-product-rating .star-rating {
    height: 3em;
}
.woocommerce .woocommerce-product-rating .star-rating {
    margin: .5em 4px 0 0;
    float: left;
}

div#blog-box.innerpage-whitebox {
    padding: 0;
    margin: 0;
}
.single_post i.fa.fa-calendar-o {
    padding-right: 10px;
}
div#commentsAdd .comment-form [class*="comment-form"] {
    width: 100%;
}
main#innerpage-box #comments li.comment {
    background: transparent;
    border: 0;
}
.woocommerce ul.products li.product .price del, 
.woocommerce-page ul.products li.product .price del {
    font-size: 15px;
}
.single-productpage h2.woocommerce-loop-product__title {
    line-height: 33px;
    word-break: break-word;
}
main#innerpage-box div#commentsAdd textarea#comment {
    background: transparent;
}
div#respond textarea#comment {
    background: transparent;
}
div#comments input[type="submit"] {
    padding: 10px 20px;
    border-radius: 5px;
    height: 50px;
}
main#innerpage-box .woocommerce-product-search .search-field {
    border: 1px solid #e9e7e7;
    background: transparent;
}
.woocommerce div.product .woocommerce-tabs .panel {
    background: transparent !important;
        border: none !important;
}
div#secondary .social-profile-icons ul li i {
    padding:5px 5px 5px 5px;
    margin: 0;
    font-size: 20px;
}

/*-----------------------
## Start search
-----------------------------*/
main#innerpage-box h2 a {
    color: #000;
    font-weight: 600;
    margin-bottom: 10px;
}
.entry-readmore a {
    padding: 2px 20px;
    color: #FFF;
    border: 0;
    -moz-transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border-radius: 5px;
    box-shadow: 0px 6px 10px -4px rgba(0,0,0,0.4);
}
/*-----------------------
## End search  
-----------------------------*/

.inner-blog-post{
       /* padding: 2em; */
    /* border: 1px solid #eaeaea; */
    margin: 1em 0;
    border-radius: 10px;
}
.inner-blogpost {
    position: relative;
    -moz-box-shadow: -1px 0px 9px rgba(152, 152, 131, 0.2);
    -webkit-box-shadow: -1px 0px 9px rgba(152, 152, 131, 0.2);
    box-shadow: -1px 0px 9px rgba(152, 152, 131, 0.2);
}
main#innerpage-box .ht-blog-thumbnail {
    overflow: hidden;
    position: relative;
}
main#innerpage-box .blog-date {
    font-size: 15px;
    line-height: 25px;
    font-weight: 500;
    word-break: break-word;
    margin: 15px 0;
}

main#innerpage-box .ht-blog-thumbnail img {
    width: 100%;
    height: auto;
    transform: scale(1);
    transition: all 1s ease 0s;
}
main#innerpage-box .ht-blog-thumbnail:hover img {
        opacity: 0.75;
    transform: scale(1.1);
}
#innerpage-box .inner-blogpost .blog-date span i {
    color: #000;
    padding-right: 5px;
}#innerpage-box .inner-blogpost-info .readmore{margin-top: 25px;}
#innerpage-box .inner-blogpost-info .readmore a{
    padding: 10px 20px;
    color: #fff;
    font-size: 15px;
    font-weight: 600;
    background: #000;
    transition: all 1s ease 0s;
}
main#innerpage-box .ht-blog-thumbnail .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    opacity: 0;
    background: linear-gradient(0deg,#516ced,#8972ea 80%) no-repeat;
    transition: all .4s ease;
}
main#innerpage-box .inner-blogpost:hover .ht-blog-thumbnail .overlay {
    opacity: 0.5;
    transform: rotate(360deg) scale(2);
}

main#innerpage-box .ht-blog-thumbnail .overlay .box {
    display: table;
    width: 100%;
    height: 100%;
}
main#innerpage-box .ht-blog-thumbnail .overlay .box .content {
    display: table-cell;
    vertical-align: middle;
    text-align: center;
}
main#innerpage-box .ht-blog-thumbnail .overlay .box .content > a {
    border-radius: 50%;
    font-size: 18px;
    width: 50px;
    height: 50px;
    background: #FFFFFF;
    color: #c9593f;
    text-align: center;
    line-height: 50px;
    transform: scale(1.2);
    transition: all 500ms ease 0s;
}
main#innerpage-box .inner-blogpost:hover .ht-blog-thumbnail .overlay .box .content > a {
    transform: scale(1);
    transition-delay: 0.1s;
    opacity: 1;
}
.inner-blogpost-info {
      margin-bottom: 15px;
    padding:2.5em 1em 1em 1em;
    border-radius: 0 0 5px 5px;
    position: relative;
}

main#innerpage-box #blog-box h2 {
    margin-bottom: 10px;
    font-size: 20px;
    font-weight: 600;
    word-break: break-word;
}
.inner-blog-excerpt {
    margin-bottom: 24px;
    font-size: 15px;
    line-height: 25px;
    font-weight: 400;
    word-break: break-word;
    margin: 12px 0;
}
main#innerpage-box #blog-box .socialMedia {
     position: absolute;
    right: 5px;
    top: 5px;
}

main#innerpage-box #blog-box .socialMedia ul{
    margin:0;
    padding:0;
    list-style:none;
    z-index: 9999;
    position: relative;
}
main#innerpage-box #blog-box .socialMedia ul li{
    display:inline-block;
    margin-left: -50px;
    float: left;
    transition:all 0.5s;
    -moz-transition:all 0.5s;
    -o-transition:all 0.5s;
    -webkit-transition:all 0.5s;
    -ms-transition:all 0.5s;
    text-align: center;
}
main#innerpage-box #blog-box .socialMedia ul li a.site-button{
    border-radius: 50%;
    width: 45px;
    line-height: 45px;
    height: 45px;
    display: block;
    color: #fff;
    background: #516bec;
}
main#innerpage-box #blog-box .socialMedia ul li a.site-button:hover{
    background:#c9593f;
}
main#innerpage-box #blog-box .socialMedia ul:hover li.share-button a.site-button{
    background:#c9593f;
}   
main#innerpage-box #blog-box .socialMedia ul:hover li{
    margin-left: 4px;
}

#blog-box .blog-title {
    text-align: center;
    margin-bottom: 2em;
}

#blog-box .blog-title .blog-list-title {
    font-weight: bold;
    font-size: 35px;
    line-height: 35px;
    color: #000;
    text-transform: capitalize;
}}

#blog-box .blog-title .blog-list-title:after {
    color: #c9593f;
}


#blog-box .ht-blog-date, #blog-box .ht-blog-date .fa{
    color: #c9593f;
        margin-bottom: 5px;
}
#blog-box .ht-blog-date .fa{
   margin-right: 5px;
}

#blog-box .btn5 a {
    position: relative;
    overflow: hidden;
    display: inline-block;
    text-decoration: none;
    font-weight: 700;
    font-size: 14px;
    padding: 1em 1.5em;
    color: #fff;
    transition: all 0.5s;
    border-radius: 0px;
    cursor: pointer;
    text-align: center;
    z-index: 9;
    word-break: break-all;
    background-color: #c9593f;
    border: none;
    margin-top: 1.5em;
}
#blog-box .btn5 a:after {
  position: absolute;
  content: "";
  width: 100%;
  height: 0;
  bottom: 0;
  left: 0;
  z-index: -1;
  border-radius: 0px;
   background-color: #4dccc6;
/*background-image: linear-gradient(315deg, #4dccc6 0%, #96e4df 74%);*/
  transition: all 0.3s ease;
}
#blog-box .btn5 a:hover {
  color: #fff;
}
#blog-box .btn5 a:hover:after {
  top: 0;
  height: 100%;
  background-color: #000;
}
#blog-box .btn5 a:active {
  top: 2px;
}
#blog-box .btn5 a i {
    color: #000;
    font-weight: 300;
    padding: 5px;
    background: #fff;
    margin-left: 8px;
    font-size: 16px;
}

.ht-blog-thumbnail .socialMedia {
    position: absolute;
    bottom: 10px;
    left: 0;
    right: 0;
    text-align: center;
    z-index: 3;
    transition: .5s;
}
.ht-blog-thumbnail .socialMedia a {
       font-size: 18px;
    padding: 5px 10px;
    line-height: 18px;
    margin: 0 0px;
    transition: 0s;
    background: #c9593f;
       border-radius: 5px;
    color: #ffffff;
    font-weight: bold;
      box-shadow: 0px 6px 10px -4px rgba(0,0,0,0.4);
    display: inline-block;
}
.ht-blog-thumbnail .socialMedia a:hover {
    background: #fff;
}
/*-----------------------
## Blog category Page 
-----------------------------*/
.inner_contentbox {
    position: relative;
    top: 0em;
    background: #fff;
    padding: 4em 0;
    border-radius: 10px;
}

/*--------------------------------------------------------------
## Inner Page Heading
--------------------------------------------------------------*/
body.page-template-default main#innerpage-box h1,
body.page-template-default main#innerpage-box h2,
body.page-template-default main#innerpage-box h3,
body.page-template-default main#innerpage-box h4,
body.page-template-default main#innerpage-box h5,
body.page-template-default main#innerpage-box h6,

.page-template-templates main#innerpage-box h1,
.page-template-templates main#innerpage-box h2,
.page-template-templates main#innerpage-box h3,
.page-template-templates main#innerpage-box h4,
.page-template-templates main#innerpage-box h5,
.page-template-templates main#innerpage-box h6
 {
    word-break: break-word;
    font-weight: 500;
    margin-bottom: 40px;
}
body.page-template-default main#innerpage-box h1:after,
body.page-template-default main#innerpage-box h2:after,
body.page-template-default main#innerpage-box h3:after,
body.page-template-default main#innerpage-box h4:after,
body.page-template-default main#innerpage-box h5:after,
body.page-template-default main#innerpage-box h6:after,

.page-template-templates main#innerpage-box h1:after,
.page-template-templates main#innerpage-box h2:after,
.page-template-templates main#innerpage-box h3:after,
.page-template-templates main#innerpage-box h4:after,
.page-template-templates main#innerpage-box h5:after,
.page-template-templates main#innerpage-box h6:after {
    content: "";
    display: block;
    width: 50px;
    height: 2px;
    margin: 0;
    position: relative;
    top:15px;
}
.blog .page-main-header {
    padding-top: 1em;
}
#innerpage-box .wp-block-image figure {
    overflow: hidden;
    position: relative;
    /*border-radius: 6px;*/
    /*-webkit-transition: all 300ms cubic-bezier(0.34, 1.61, 0.7, 1);*/
    /*-moz-transition: all 300ms cubic-bezier(0.34, 1.61, 0.7, 1);*/
    /*-o-transition: all 300ms cubic-bezier(0.34, 1.61, 0.7, 1);*/
    /*-ms-transition: all 300ms cubic-bezier(0.34, 1.61, 0.7, 1);*/
    /*transition: all 300ms cubic-bezier(0.34, 1.61, 0.7, 1);*/
    /*-webkit-transform: translate(0, 0);*/
    /*-ms-transform: translate(0, 0);*/
    /*transform: translate(0, 0);*/
    /*-webkit-box-shadow: 0 16px 38px -12px rgba(0,0,0,0.56), 0 4px 25px 0 rgba(0,0,0,0.12), 0 8px 10px -5px rgba(0,0,0,0.2);*/
    /*-moz-box-shadow: 0 16px 38px -12px rgba(0,0,0,0.56),0 4px 25px 0 rgba(0,0,0,0.12),0 8px 10px -5px rgba(0,0,0,0.2);*/
    /*box-shadow: 0 16px 38px -12px rgba(0,0,0,0.56), 0 4px 25px 0 rgba(0,0,0,0.12), 0 8px 10px -5px rgba(0,0,0,0.2);*/
}

  .page-template-templates main#innerpage-box #blog-box h2:after {
    display: none;
 }
 #innerpage-box .widget-area .widget-title {
    font-size: 16px;
    color: #000;
    font-weight: 700;
    background-color: transparent;
    /*text-transform: uppercase;*/
    margin: 0 0 20px 0;
    padding-bottom: 10px;
   /* border-bottom: 2px solid #e5e5e5;*/
    position: relative;
    display: block;
    text-align: left;
    border-radius: 0;
}
.widget-area .widget h4:after {
    position: absolute;
    content: "";
    width: 38%;
    height: 2px;
    background-color: #000;
    bottom: 0;
    left: 0;
}
main#innerpage-box .widget-area .tagcloud a {
    position: relative;
    box-shadow: unset;

    position: relative;
    display: inline-block;
    line-height: 24px;
    padding: 5px 15px 5px;
    margin: 3px 5px 3px 5px;
    text-align: center;
    /* font-size: 14px !important; */
    background: none;
    font-weight: 400;
    border-radius: 6px;
    border: 1px solid #ebebeb;
    transition: all 300ms ease;
    -webkit-transition: all 300ms ease;
    -ms-transition: all 300ms ease;
    -o-transition: all 300ms ease;
    -moz-transition: all 300ms ease;
}
.widget-area .tagcloud {
    list-style: none;
    padding: 4px 0;
    margin: 0;
    border: 1px solid #eaeaea;
   /* border-bottom: none;*/
}
.widget-area .textwidget {
    border: 1px solid #eaeaea;
    padding: 10px;
}
.widget-area li span.post-date {
    margin-bottom: 1em;
    position: relative;
    display: list-item;
}
.widget-area .widget_media_image img {
    padding: 10px;
    margin: 0;
    border: 1px solid #eaeaea;
}
#secondary .gallery-columns-3 {
     padding: 10px;
    margin: 0;
    border: 1px solid #eaeaea;
}
#secondary .gallery-columns-3 .gallery-item a{
    padding: 0;
}
.widget-area div#calendar_wrap {
    padding: 0px 5px;
    margin: 0;
    border: 1px solid #eaeaea;
}
.widget_calendar table {
    border-collapse: separate;
    width: 100%;
}
body.page-template-default #innerpage-box .widget-area .widget-title:after ,
 .page-template-templates #innerpage-box .widget-area .widget-title:after {
       position: relative;
    content: "";
    width: 35%;
    height: 2px;
    background-color: #000;
    bottom: 0;
    left: 0;
    background-image: none;
    display: block;
    /* width: 50px; */
    /* height: 3px; */
    margin: 0;
    /* position: relative; */
    top: 11px;
}
.widget-area .woocommerce-product-search, .widget-area form#searchform {
    width: 100%;
    border: 1px solid #eaeaea;
    padding: 10px;
}
.widget-area ul ul li a:before {
    font-family: 'FontAwesome';
    transition: all 0.3s ease;
    content: "\f105";
    margin: 0 5px 0;
}
.widget-area ul ul {
    border-radius: 0;
    border:none;
}
.widget-area ul ul li {
    padding-left: 10px;
    border-bottom: none;
}
.widget-area ul ul li a {
    padding: 0;
}
.widget-area ul ul li.current_page_item > a,
.widget-area ul ul li.current_page_item > a:hover,
div#secondary li.current_page_item > a:before{ background:none; }
.widget_calendar table thead tr th {
    font-size: 12px;
    padding: 10px;
    text-align: center;
    border: none;
    color: #fff;
}
.widget_calendar table tbody td {
    font-size: 13px;
    padding: 6px 5px;
    text-align: center;
    background-color: transparent;
    border: none;
    color: #444;
}
.widget_calendar tfoot tr td {
    border: none;
    padding: 0px;
}
/*--------------------------------------------------------------
## Odometer CSS
--------------------------------------------------------------*/
.odometer.odometer-auto-theme, 
.odometer.odometer-theme-default {
    display: block;
    vertical-align: middle;
    *vertical-align: auto;
    *zoom: 1;
    *display: inline;
    position: relative;
}
.odometer.odometer-auto-theme .odometer-digit, 
.odometer.odometer-theme-default .odometer-digit {
    display: inline-block;
    vertical-align: middle;
    *vertical-align: auto;
    *zoom: 1;
    *display: inline;
    position: relative;
}
.odometer.odometer-auto-theme .odometer-digit .odometer-digit-spacer, 
.odometer.odometer-theme-default .odometer-digit .odometer-digit-spacer {
    display: inline-block;
    vertical-align: middle;
    *vertical-align: auto;
    *zoom: 1;
    *display: inline;
    visibility: hidden;
}
.odometer.odometer-auto-theme .odometer-digit .odometer-digit-inner, 
.odometer.odometer-theme-default .odometer-digit .odometer-digit-inner {
    text-align: left;
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    overflow: hidden;
}
.odometer.odometer-auto-theme .odometer-digit .odometer-ribbon, 
.odometer.odometer-theme-default .odometer-digit .odometer-ribbon {
    display: block;
}
.odometer.odometer-auto-theme .odometer-digit .odometer-ribbon-inner, 
.odometer.odometer-theme-default .odometer-digit .odometer-ribbon-inner {
    display: block;
    -webkit-backface-visibility: hidden;
}
.odometer.odometer-auto-theme .odometer-digit .odometer-value, 
.odometer.odometer-theme-default .odometer-digit .odometer-value {
    display: block;
    -webkit-transform: translateZ(0);
}
.odometer.odometer-auto-theme .odometer-digit .odometer-value.odometer-last-value, 
.odometer.odometer-theme-default .odometer-digit .odometer-value.odometer-last-value {
    position: absolute;
}
.odometer.odometer-auto-theme.odometer-animating-up .odometer-ribbon-inner, 
.odometer.odometer-theme-default.odometer-animating-up .odometer-ribbon-inner {
    -webkit-transition: -webkit-transform 2s;
    -moz-transition: -moz-transform 2s;
    -ms-transition: -ms-transform 2s;
    -o-transition: -o-transform 2s;
    transition: transform 2s;
}
.odometer.odometer-auto-theme.odometer-animating-up.odometer-animating .odometer-ribbon-inner, 
.odometer.odometer-theme-default.odometer-animating-up.odometer-animating .odometer-ribbon-inner {
    -webkit-transform: translateY(-100%);
    -moz-transform: translateY(-100%);
    -ms-transform: translateY(-100%);
    -o-transform: translateY(-100%);
    transform: translateY(-100%);
}
.odometer.odometer-auto-theme.odometer-animating-down .odometer-ribbon-inner, 
.odometer.odometer-theme-default.odometer-animating-down .odometer-ribbon-inner {
    -webkit-transform: translateY(-100%);
    -moz-transform: translateY(-100%);
    -ms-transform: translateY(-100%);
    -o-transform: translateY(-100%);
    transform: translateY(-100%);
}
.odometer.odometer-auto-theme.odometer-animating-down.odometer-animating .odometer-ribbon-inner, 
.odometer.odometer-theme-default.odometer-animating-down.odometer-animating .odometer-ribbon-inner {
    -webkit-transition: -webkit-transform 2s;
    -moz-transition: -moz-transform 2s;
    -ms-transition: -ms-transform 2s;
    -o-transition: -o-transform 2s;
    transition: transform 2s;
    -webkit-transform: translateY(0);
    -ms-transform: translateY(0);
    -o-transform: translateY(0);
    transform: translateY(0);
}

.odometer.odometer-auto-theme .odometer-value, 
.odometer.odometer-theme-default .odometer-value {
    text-align: center;
}

/*--------------------------------------------------------------
## WooCommerce CSS
--------------------------------------------------------------*/
.woocommerce ul.products li.product, 
.woocommerce-page ul.products li.product{
    padding-bottom: 45px !important;
  /*  border: 4px solid #f1f0f0 !important;*/
}
.woocommerce ul.products li.product:hover, 
.woocommerce-page ul.products li.product:hover {
 /*border-bottom: 1px solid #6a1b9a !important;*/
}
.woocommerce ul.products li.product:hover, 
.woocommerce-page ul.products li.product:hover{
    border-color: #6a1b9a;
}
 main#innerpage-box h2.woocommerce-loop-product__title {
    font-weight: 600;
 }

.woocommerce ul.products li.product a img{
    border: 5px solid #f1f0f0 !important;
    /*height: 230px;*/
}
.woocommerce div.product div.images img {
    border: 2px solid #f1f0f0 !important;
}

.woocommerce ul.products li.product .total-product-title-wrap{
    padding: 20px 10px 0;
}

.woocommerce ul.products li.product a{
    color: inherit;
}

.woocommerce ul.products li.product .woocommerce-loop-category__title, 
.woocommerce ul.products li.product .woocommerce-loop-product__title, 
.woocommerce ul.products li.product h3{
    margin: 0 0 6px;
    padding: 0;
}
.woocommerce div.product div.images .flex-control-thumbs{
    margin: 5px -5px 0;
}
.woocommerce div.product div.images .flex-control-thumbs li{
    padding: 5px;
}
.woocommerce ul.products li.product .price{
    font-weight: 500;
    font-size: 20px;
    color: #fe5722;
    margin: 0;
}
.woocommerce ul.products li.product .price del{
    display: inline;
    color: #999;
    opacity: 1;
}
.woocommerce ul.products li.product .price ins{
    display: inline;
    font-weight: 400;
}
.woocommerce .cart-collaterals .cart_totals, .woocommerce-page .cart-collaterals .cart_totals {
    float: right;
    width: 100%;
}
.woocommerce-MyAccount-navigation ul li:before{
    display: none;
}
.woocommerce-MyAccount-navigation ul li a{
    background: none;
    font-size: 16px;
    margin-bottom: 5px;
  display: inline-block;
    color:#fc856d;
    padding: 0px 20px;

}
.woocommerce-MyAccount-navigation ul li{
    margin: 5px 0 !important;
}
.woocommerce-MyAccount-navigation-link.is-active a{
    color:#000 !important;
}
.woocommerce #respond input#submit, 
.woocommerce a.button, 
.woocommerce button.button, 
.woocommerce input.button {
    padding: 10px 20px;
    font-weight: 400;
    border-radius: 5px;
    color: #FFF;
    background-color: #fe5722;
    border:1px solid #fe5722;    
    margin-bottom: -20px;
    line-height: 20px;
    font-size: 0.9em;
}
.woocommerce #respond input#submit:hover, 
.woocommerce a.button:hover, 
.woocommerce button.button:hover, 
.woocommerce input.button:hover{
    background: #333;
   /* border:1px solid #333;*/
    color: #FFF;
}

.woocommerce #respond input#submit,
.woocommerce a.button,
.woocommerce button.button,
.woocommerce input.button {
    color:#fff !important;
}

.woocommerce ul.products li.product .button{
    margin: 0;
    transform: translateY(50%);
    -ms-transform: translateY(50%);
    -webkit-transform: translateY(50%);
    font-size: 16px;
    background-color: #ec5597;
    border: 1px solid #ec5597;
    padding: 10px 30px;
    color: #FFF;
    border: 0;
    -moz-transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border-radius: 5px;
}
.woocommerce ul.products li.product .button:hover{
    opacity:0.8;
     transition: all 0.3s ease;
}
.woocommerce div.product .product_title {
    clear: none;
    margin-top: 0;
    padding: 0;
    font-size: 24px;
    color: #000;
    font-weight: 400;
    line-height: 1.2;
    margin-bottom: 6px;
    border-bottom: 1px solid #e1e1e1;
    padding-bottom: 8px;
    margin-bottom: 20px;
}

.single_inner .page-main-header {
    display: none;
}

.woocommerce ul.products li.product:hover .button{
    border-color: #fe5722;
    background-color: #fe5722;
    color: #FFF;
}

.woocommerce ul.products li.product .button.loading{
    opacity: 1;
}

.woocommerce a.added_to_cart{
    padding-top: 0;
    position: absolute;
    left: 0;
    right: 0;
    top: 100%;
    margin-top: 25px;
    font-size: 13px;
}

.woocommerce #respond input#submit.alt, 
.woocommerce a.button.alt, 
.woocommerce button.button.alt, 
.woocommerce input.button.alt{
    border-color: #fe5722;
    background-color: #fe5722;
}

.woocommerce #respond input#submit.alt:hover, 
.woocommerce a.button.alt:hover, 
.woocommerce button.button.alt:hover, 
.woocommerce input.button.alt:hover{
    background: #333;
    border-color: #333;
    color: #FFF;
}

.woocommerce nav.woocommerce-pagination ul{
    border: 0;
    margin: 0;
}

.woocommerce nav.woocommerce-pagination ul li{
    border: 0;
    margin: 0 4px;
}

.woocommerce nav.woocommerce-pagination ul li a, 
.woocommerce nav.woocommerce-pagination ul li span{
    display: block;
    background: #fe5722;
    color: #FFF;
    padding: 8px 12px;
    line-height: 1;
    border-radius: 2px;
}

.woocommerce nav.woocommerce-pagination ul li a:focus, 
.woocommerce nav.woocommerce-pagination ul li a:hover, 
.woocommerce nav.woocommerce-pagination ul li span.current{
    background: #333;
    color: #FFF;
}

.woocommerce span.onsale{
    min-height: 0;
    min-width: 0;
    padding: 0 15px;
    font-weight: 400;
    line-height: 26px;
    border-radius: 0;
    background-color: #fe5722;
    color: #fff;
    font-size: 14px;
    margin: 10px 0 0 -6px;
    top: 0;
    left: 0;
}

.woocommerce span.onsale:after{
    border-color: transparent #e8ae00 #e8ae00 transparent;
    border-width: 3px;
    border-style: solid;
    content: "";
    position: absolute;
    bottom: 100%;
    left: 0px;
}

.woocommerce ul.products li.product .onsale{
    margin: 10px -6px 0 0;
}

.woocommerce ul.products li.product .onsale:after{
    border-color: transparent transparent #e8ae00 #e8ae00;
    border-width: 3px;
    border-style: solid;
    right: 0px;
    left: auto;
}

.woocommerce div.product p.price, 
.woocommerce div.product span.price{
    color: #fe5722
}

.woocommerce div.product p.price ins, 
.woocommerce div.product span.price ins{
    font-weight: 400;
}

.woocommerce .product_meta{
    font-size: 15px;
}

.woocommerce .product_meta a{
    color: inherit;
}

.woocommerce .product_meta a:hover{
    color: #fe5722;
}

.woocommerce div.product .woocommerce-tabs ul.tabs{
    padding: 0;
    border-bottom: 2px solid #fe5722;
    padding-bottom: 10px;
}

.woocommerce div.product .woocommerce-tabs ul.tabs li.active:before,
.woocommerce div.product .woocommerce-tabs ul.tabs li.active:after,
.woocommerce div.product .woocommerce-tabs ul.tabs li:before,
.woocommerce div.product .woocommerce-tabs ul.tabs li:after,
.woocommerce div.product .woocommerce-tabs ul.tabs:before{
    display: none !important;
}

.woocommerce div.product .woocommerce-tabs ul.tabs li a {
    padding: 10px 0;
    font-weight: 400;
}

.woocommerce div.product .woocommerce-tabs ul.tabs li {
    border: 0;
    background-color: #333;
    border-radius: 0;
    margin: 0 6px 0 0;
    padding: 0 20px;
}

.woocommerce div.product .woocommerce-tabs ul.tabs li.active{
    background: #fe5722;
}

.woocommerce div.product .woocommerce-tabs ul.tabs li a{
    color: #FFF !important;
}

.woocommerce #reviews #comments h2,
.woocommerce #reviews h3{
    font-size: 22px;
    margin-bottom: 30px !important;
}

.woocommerce-Reviews .comment-form-author, 
.woocommerce-Reviews .comment-form-email{
    width: 100%;
}

.woocommerce-Reviews .comment-form-author input,
.woocommerce-Reviews .comment-form-email input{
    width: 100%;
}

.related.products h2{
    margin-bottom: 30px;
}

.woocommerce #respond input#submit.disabled, 
.woocommerce #respond input#submit:disabled, 
.woocommerce #respond input#submit:disabled[disabled], 
.woocommerce a.button.disabled, .woocommerce a.button:disabled, 
.woocommerce a.button:disabled[disabled], 
.woocommerce button.button.disabled, 
.woocommerce button.button:disabled, 
.woocommerce button.button:disabled[disabled], 
.woocommerce input.button.disabled, 
.woocommerce input.button:disabled, 
.woocommerce input.button:disabled[disabled]{
    background: #fe5722;
    padding: 10px 20px;
    color: #FFF;
}

.woocommerce #respond input#submit.disabled:hover, 
.woocommerce #respond input#submit:disabled:hover, 
.woocommerce #respond input#submit:disabled[disabled]:hover, 
.woocommerce a.button.disabled:hover, 
.woocommerce a.button:disabled:hover, 
.woocommerce a.button:disabled[disabled]:hover, 
.woocommerce button.button.disabled:hover, 
.woocommerce button.button:disabled:hover, 
.woocommerce button.button:disabled[disabled]:hover, 
.woocommerce input.button.disabled:hover, 
.woocommerce input.button:disabled:hover, 
.woocommerce input.button:disabled[disabled]:hover{
    background: #333;
    color: #FFF;
}

.woocommerce #respond input#submit.alt.disabled, 
.woocommerce #respond input#submit.alt.disabled:hover, 
.woocommerce #respond input#submit.alt:disabled, 
.woocommerce #respond input#submit.alt:disabled:hover, 
.woocommerce #respond input#submit.alt:disabled[disabled], 
.woocommerce #respond input#submit.alt:disabled[disabled]:hover, 
.woocommerce a.button.alt.disabled, 
.woocommerce a.button.alt.disabled:hover, 
.woocommerce a.button.alt:disabled, 
.woocommerce a.button.alt:disabled:hover, 
.woocommerce a.button.alt:disabled[disabled], 
.woocommerce a.button.alt:disabled[disabled]:hover, 
.woocommerce button.button.alt.disabled, 
.woocommerce button.button.alt.disabled:hover, 
.woocommerce button.button.alt:disabled, 
.woocommerce button.button.alt:disabled:hover, 
.woocommerce button.button.alt:disabled[disabled], 
.woocommerce button.button.alt:disabled[disabled]:hover, 
.woocommerce input.button.alt.disabled, 
.woocommerce input.button.alt.disabled:hover, 
.woocommerce input.button.alt:disabled, 
.woocommerce input.button.alt:disabled:hover, 
.woocommerce input.button.alt:disabled[disabled], 
.woocommerce input.button.alt:disabled[disabled]:hover{
    background: #fe5722;
    border-color: #fe5722;
}

#add_payment_method .wc-proceed-to-checkout a.checkout-button, 
.woocommerce-cart .wc-proceed-to-checkout a.checkout-button, 
.woocommerce-checkout .wc-proceed-to-checkout a.checkout-button{
    line-height: 40px;
    font-size: inherit;
    padding: 0;
}

.select2-container .select2-choice{
    border: 1px solid #EEE;
    font-size: 0.9em;
    color: inherit;
    border-radius: 0;
}

.select2-results{
    font-size: 0.9em;
    color: inherit;
}

.select2-drop-active{
    border-color: #EEE;
}

.select2-drop.select2-drop-above.select2-drop-active{
    border-color: #EEE;
}

.woocommerce-error, 
.woocommerce-info, 
.woocommerce-message{
    border-top-color: #fe5722;
    font-size: 0.9em;
    margin-bottom: 15px;
}

.woocommerce-error:before, 
.woocommerce-info:before, 
.woocommerce-message:before{
    color: #fe5722;
}

.woocommerce-error a.button, 
.woocommerce-info a.button, 
.woocommerce-message a.button{
    line-height: 1;
    height: auto;
    margin-right: 0px;
    padding: 10px 20px;
    color: #FFF;
    border: 0;
    -moz-transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border-radius: 5px;
    box-shadow: 0px 6px 10px -4px rgba(0,0,0,0.4);
}

.woocommerce table.shop_table tbody th, 
.woocommerce table.shop_table tfoot td, 
.woocommerce table.shop_table tfoot th{
    font-weight: 400;
}

#customer_details{
    margin-bottom: 40px;
}

.woocommerce form.checkout_coupon, 
.woocommerce form.login, 
.woocommerce form.register{
    border: 1px solid #EEE;
    border-radius: 0;  
      padding: 10px;
}

.woocommerce #payment #place_order, 
.woocommerce-page #payment #place_order{
    float: none;
    border: 1px solid;
        padding: 10px 20px;
    color: #FFF;
    border: 0;
    -moz-transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border-radius: 5px;
    box-shadow: 0px 6px 10px -4px rgba(0,0,0,0.4);
}

.woocommerce .widget_shopping_cart .cart_list li, 
.woocommerce.widget_shopping_cart .cart_list li{
    padding-top: 4px;
}

.woocommerce ul.cart_list li a, 
.woocommerce ul.product_list_widget li a{
    font-weight: 400;
}

.woocommerce a.remove{
    font-family: Arial;
    font-weight: normal;
}

.woocommerce .widget_shopping_cart .cart_list li a.remove, 
.woocommerce.widget_shopping_cart .cart_list li a.remove{
    top: 8px;
}

.woocommerce .widget_layered_nav ul li{
    padding: 5px 0;
}

.woocommerce .widget_price_filter .ui-slider .ui-slider-handle{
    background-color: #FFF;
    border: 4px solid #fe5722;
}

.woocommerce .widget_price_filter .ui-slider .ui-slider-range{
    background-color: #fe5722
}

.woocommerce .widget_price_filter .price_slider_wrapper .ui-widget-content{
    background-color: #EEE;
}

.woocommerce .widget_price_filter .ui-slider .ui-slider-handle{
    height: 20px;
    width: 20px;
    top: -6px;
    margin-left: -10px;
}

.woocommerce-account .woocommerce-MyAccount-navigation ul{
    list-style: none;
}

.woocommerce-MyAccount-navigation-link a{
    background: #fe5722;
    color: #FFF;
    padding: 10px 20px;
    margin-bottom: 5px;
    display: block;
}

.woocommerce-MyAccount-navigation-link.is-active a,
.woocommerce-MyAccount-navigation-link a:hover{
    background: none;
}

.woocommerce ul.products li.product strong {
    display: inline-block;
}

/* Testimonial slider css */
.quote {
    color: rgba(0,0,0,.1);
    text-align: center;
    margin-bottom: 30px;
}
/*-------------------------------*/
/*    Carousel Fade Transition   */
/*-------------------------------*/
.carousel-indicators li{
    background-color: #6963635c;
    border: 1px solid #6963635c;
}
/* GALLARY HOMEPAGE CSS */
main#innerpage-box div#ht-contactus-wrap a:hover {
    color: #444;
}
/**/
ul.themetext {
    margin: 0;
    padding: 0;
    list-style: none;
}
@-webkit-keyframes blinker {
  from {opacity: 1.0;}
  to {opacity: 0.0;}
}
.blink{
    text-decoration: blink;
    -webkit-animation-name: blinker;
    -webkit-animation-duration: 0.6s;
    -webkit-animation-iteration-count:infinite;
    -webkit-animation-timing-function:ease-in-out;
    -webkit-animation-direction: alternate;
}
.ht-site-title, .site-title {
    margin: 0;
}

div#content li.blocks-gallery-item:before {
    content: none;
}
div#content li.blocks-gallery-item {
    padding: 14px 14px 0 14px;
}
div#content li.blocks-gallery-item img:hover {
    opacity: 0.5;
}


.single_post .post-date-publishable {
    padding-top: 15px;
    border-bottom: 1px solid #ccc;
    padding-bottom: 15px;
    margin-bottom: 15px;
}
.woocommerce-product-search .search-field {
    width: 100% !important;
    margin-bottom: 5px;
    margin-top: 20px;
}
.single-productpage #sidebars button {
    padding: 7px 25px;
    border: 1px solid;
    width: 100%;
    padding: 10px 20px;
    color: #FFF;
    border: 0;
    -moz-transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border-radius: 5px;
    box-shadow: 0px 6px 10px -4px rgba(0,0,0,0.4);
        margin-bottom: 0px;
}
div#sidebars ins {
    background: none;
}
.widget-area span.woocommerce-Price-amount.amount {
    margin: 0 0 10px 0;
    line-height: 24px;
    min-height: 26px;
    color: #242424;
    font-size: 17px;
    font-weight: 500;
}
.widget-area del span.woocommerce-Price-amount.amount {
    font-size: 14px;
    color: #666;
}
.widget-area span.woocommerce-Price-amount.amount {
    margin: 0 0 10px 0;
    line-height: 24px;
    min-height: 26px;
    color: #242424;
    font-size: 17px;
    font-weight: 500;
}

/*--------------------------------------------------------------
# rating
--------------------------------------------------------------*/
.woocommerce .star-rating {
    float: right;
    position: relative;
    height: 3em;
    line-height: 1;
    font-size: 1em;
    width: 100%;
    font-family: star; 
    overflow: initial;
}
.woocommerce .star-rating span {
    overflow: hidden;
    float: left;
    top: 0px;
    left: 0;
    position: absolute;
    padding-top: 1.5em;
}
.woocommerce .star-rating span::before {
    content: '\53\53\53\53\53';
    top: 0;
    position: absolute;
    left: 0;
    display: none;
}
.woocommerce .star-rating::before {
    content: '\73\73\73\73\73';
    color: #d3ced2;
    float: left;
    top: 0;
    left: 0;
    position: absolute;
    /*display: none;*/
}
.comment-text .star-rating {
    margin-top: 0;
    margin-right: 0;
    margin-bottom: 0;
}
.woocommerce .star-rating, .woocommerce-page .star-rating {
    margin-bottom: 0 !important;
}
/*--------------------------------------------------------------
# rating
--------------------------------------------------------------*/
.woocommerce div.product form.cart .button, .woocommerce-page div.product form.cart .button, .woocommerce #content div.product form.cart .button, .woocommerce-page #content div.product form.cart .button {
    min-width: 180px;
    background-color: #000;
    color: #fff;
    min-width: 180px;
    color: #fff;
    padding: 0 25px;
    height: 50px;
    -webkit-transition: all 0.5s;
    transition: all 0.5s;
    color: #000;
    font-size: 16px;
    font-weight: 600;
    text-align: center;
    border: none;
    border-radius: 0;
    opacity: 1;
}
.woocommerce .quantity .qty {
    height: 50px !important;
    width: 63px;
    padding-right: 4px;
    margin-right: 25px;
}
.woocommerce #content div.product .woocommerce-tabs ul.tabs li, .woocommerce div.product .woocommerce-tabs ul.tabs li, .woocommerce-page #content div.product .woocommerce-tabs ul.tabs li, .woocommerce-page div.product .woocommerce-tabs ul.tabs li {
        padding: 0px 20px;
    color: #FFF;
    border: 0;
    -moz-transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border-radius: 5px;
    box-shadow: 0px 6px 10px -4px rgba(0,0,0,0.4);
}
.woocommerce #review_form #respond .form-submit input {
    border: 1px solid;
        padding: 10px 20px;
    color: #FFF;
    border: 0;
    -moz-transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border-radius: 5px;
    box-shadow: 0px 6px 10px -4px rgba(0,0,0,0.4);
}
.woocommerce div.product div.images img:hover {
    opacity: 0.8;
}
.woocommerce div.product .woocommerce-product-rating a, 
.woocommerce .star-rating span{
    float: right;
    display: contents;
}
.single-productpage .innerpage-whitebox .woocommerce-product-rating span.count {
    display: inherit;
}
.woocommerce .star-rating::before{
    top: 0 !important;
    left: 0 !important;
    position: relative;
}

.woocommerce div.product .woocommerce-product-rating{
    line-height: 0px;
}
.woocommerce-page.columns-3 ul.products li.product, .woocommerce.columns-3 ul.products li.product {
    overflow: hidden;
}
.woocommerce ul.products li.product a img {
    transform: scale(1);
    transition: all 1s ease 0s;
    width: 100%;
    height: 250px;
}
.woocommerce ul.products li.product a img:hover {
    opacity: 0.8;
     transform: scale(1.05);
}
.woocommerce .cart .button, .woocommerce .cart input.button {
    border: 1px solid;
    padding: 10px 20px;
    color: #FFF;
    border: 0;
    -moz-transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border-radius: 5px;
    box-shadow: 0px 6px 10px -4px rgba(0,0,0,0.4);
    margin: 0px 0;
}
#add_payment_method .wc-proceed-to-checkout a.checkout-button, 
.woocommerce-cart .wc-proceed-to-checkout a.checkout-button, 
.woocommerce-checkout .wc-proceed-to-checkout a.checkout-button {
    border: 1px solid;
        padding: 10px 20px;
    color: #FFF;
    border: 0;
    -moz-transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border-radius: 5px;
    box-shadow: 0px 6px 10px -4px rgba(0,0,0,0.4);
}
.woocommerce .widget_shopping_cart .buttons a, 
.woocommerce.widget_shopping_cart .buttons a {
    border: 1px solid;
    padding: 10px 20px;
    color: #FFF;
    border: 0;
    -moz-transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    transition: all 0.3s ease;
    border-radius: 5px;
    box-shadow: 0px 6px 10px -4px rgba(0,0,0,0.4);
}
.woocommerce .widget_price_filter .price_slider_amount .price_label {
    padding-top: 40px;
}
body.page-template-default.page.page-id-134.logged-in.admin-bar.woocommerce-checkout.woocommerce-page.woocommerce-js.ht_right_sidebar.columns-3.customize-support header#ht-masthead,
body.page-template-default.page.page-id-133.logged-in.admin-bar.woocommerce-cart.woocommerce-page.woocommerce-js.ht_right_sidebar.columns-3.customize-support #ht-masthead,
body.page-template-default.page.page-id-135.logged-in.admin-bar.woocommerce-account.woocommerce-page.woocommerce-js.ht_right_sidebar.columns-3.customize-support header#ht-masthead {
    position: relative;
}
.woocommerce table.shop_table{
    padding-bottom: 30px;
}
.sf-arrows .sf-with-ul:after {
    content: '\f107';
    font-family: FontAwesome;
    position: absolute;
    right: -2px;
    height: 0;
    width: 0;
    display: none;
}
.sf-arrows .sf-with-ul:before {
    display: none;
}
.woocommerce #reviews #comments ol.commentlist li img.avatar {
    position: relative;
}
.woocommerce #reviews #comments ol.commentlist li {
    display: inline-block;
}
.woocommerce #reviews #comments ol.commentlist li .comment-text {
    margin: 0;
}
/*-----------------------
## Single product
-----------------------------*/
.woocommerce div.product .product_meta .posted_in, .woocommerce div.product .product_meta .tagged_as {
    display: block;
    font-weight: 400;
    line-height: 1.6;
}
.woocommerce .star-rating {
    float: right;
    position: relative;
    height: 1.5em;
    line-height: 1;
    font-size: 1em;
    width: 100%;
    font-family: star;
    overflow: initial;
}
.woocommerce .widget_rating_filter ul li a {
    padding: 1px 0;
    text-decoration: none;
}
.widget-area .woocommerce li.wc-layered-nav-rating .star-rating {
    margin: 0;
}
.widget-area .woocommerce ul.product_list_widget li a {
    font-size: 13px;
    font-weight: 400;
    text-transform: capitalize;
    padding: 0px 0 8px;
}
.widget-area .woocommerce .product_list_widget li .star-rating {
    float: right;
    position: relative;
    height: 1.5em;
    line-height: 1;
    font-size: 1em;
    width: 63%;
    font-family: star;
    overflow: initial;
    margin-bottom: 10px !important;
}
main#innerpage-box .woocommerce-product-details__short-description ul li:before {
    content: '';
    width: 6px;
    height: 6px;
    background-color: #999;
    -webkit-border-radius: 50%;
    border-radius: 50%;
    position: absolute;
    top: 7px;
    left: 0;
}

main#innerpage-box .woocommerce-product-details__short-description ul li {
    position: relative;
    padding-left: 15px;
    font-size: 14px;
    margin-bottom: 7px;
    line-height: 1.4;
}
.woocommerce div.product .woocommerce-tabs .panel {
    background: transparent !important;
    border: none !important;
    box-shadow: none;
}
/*===================== responsive ==========================*/

@media screen and (min-width: 2568px) and (max-width: 2675px){

.head-menu {position: relative;padding: 1px 0;}
.navigation .mainmenu {position: relative;left: -23px;}
}

@media screen and (min-width: 2360px) and (max-width: 2567px){

.head-menu {position: relative;padding: 1px 0;}
.navigation .mainmenu {position: relative;}

}

@media screen and (min-width: 1986px) and (max-width: 2361px){

.head-menu {position: relative;padding: 1px 0;}
.navigation .mainmenu {position: relative;}

}

@media screen and (max-width: 1520px){
    .single-team .in-our-team ul > li > a {
        line-height: 30px;
        width: 30px;
        height: 30px;
    }
}
@media screen and (max-width: 1510px) and (min-width: 1445px){

    .inner-page-gallery {
        width: 20rem;
        height: 20rem;
    }
    .inner-page-gallery .text {
        right: 40%;
    }
}

@media screen and (max-width: 1380px){ 
/* .share-btn ul li a.site-button {
    width: 30px;
    line-height: 30px;
    height: 30px;
}*/


.slider_content {
    left: 52%;
}
.slider_section .extimg img.ex {
    width: auto;
    max-width: 25%;
}
.margin-sec {
    margin: 0 3em;
}
.features-area .fs-in-area:hover a.icon span {
    line-height: 110px;
    font-size: 62px;
}
.features-area .fs-in-area a.icon span:after {
    width: 80%;
    height: 85%;
    top: 8px;
    left: 12px;
}
.featured-single-area {
    margin: 0 3em;
}
.h-s-width {
    width: 25%;
}
.widget_calendar table thead tr th {
    font-size: 10px;
    padding: 3px;
}
.navigation .mainmenu {
    left: 0;
}

}
@media screen and (max-width: 1300px){
.inner-nav-clip{
    background-color: transparent!important;
}
.navigation .mainmenu>li>a {
    font-size: 13px;
    padding: 5px 12px 4px;
}
}

@media screen and (min-width: 1441px){ 
.h-s-width {
    width: 26%;
}
.head-menu {
    position: relative;
  /*  padding:1px 0 1px 60px;*/
}

}
@media screen and (min-width: 1442px) and (max-width: 1540px){
.header-share {
    padding-right: 0;
}
.slider_section .extimg img.ex{
    max-width: 30%;
}
}

@media screen and (min-width: 1024px) and (max-width: 1300px){

#slider .owl-nav button.owl-prev {left: 50px;}
#slider .owl-nav button.owl-next{right: 50px;}
header .Reg {font-size: 15px;}
#appointment h5 {  font-size: 24px;}
#appointment .appbtn a {   font-size: 22px;}
#about-section .abt-lhsbx {   padding-right: 2em;}
.service-area .service-title-box {padding: 10px 15px;}
.page-template-default #innerpage-box .service_inbox .title {margin: 0;}
 main#innerpage-box #blog-box .socialMedia ul li{margin-left: -30px;}
main#innerpage-box .ht-blog-thumbnail img {height: auto;}
/*.blog-area .blog-thumbnail img {height: 250px;}*/
#innerpage-box .lz-gallery-images img{height: 250px;}
#innerpage-box .single-team-img img {   height: 260px;}
.faq_bm .faq-heading{font-size: 50px;}
 main#innerpage-box #blog-box .socialMedia ul li a.site-button{
        width: 30px;
        line-height: 30px;
        height: 30px;
}
}



@media screen and (min-width: 320px) and (max-width: 1200px){
.slider_section .btn5 a{padding: 8px 30px;}
}

@media screen and (min-width: 1024px) and (max-width: 1200px){
#features-section .mem-inn{padding: 1.5em 10px;}
#content-box .faqimg img { height: 500px;}
#team .our-team .single-team-img img{height: 260px;}
.slider_section .sub-title {margin: 1em 0 ;}
#about-section .section-title h2 { font-size: 40px !important;}
#quote-carousel .carousel-control.left {left: -42px;}
.header-btn .head-button a{padding: 18px 25px;}
.slider_section .extimg img.ex{max-width: 20%;}
.slide-btn{bottom: 110px;}
.slide-btna a.btn{font-size: 14px;padding: 10px 35px;}
body.page-template-default main#innerpage-box .single-service-bx .service-icon img {width: 100%;height: 220px;}
.inner-page-gallery img {width: 100%;height: 200px;}
.footer-area .wpcf7:after{right: 7px;}
#quote-carousel .carousel-control.right {right: -42px;}
.slider_section .bx-wrapper .bx-controls-direction a {height: 48px;line-height: 45px;}
.ht-slider-highlighttext {font-size: 30px;}
.ht-slide-cap-title.animated.fadeInDown {font-size: 35px;}
.header-seo.header-transparent .is-fixed .main-bar input[type="search"] {top: -5px;}
.header-seo.header-transparent .is-fixed .main-bar span.sb-icon-search {top: -14px;}
.slider_section .title {font-size: 50px;line-height: 50px;}
.slider_section .sub-title {line-height: 25px;padding: 0;}
.slider_content {top: 50%;width: 568px;left: 55%;}
#slider .owl-nav button{top: 63%;display: none;}
.img-slide-responsive {right: 800px;top: -155px;}
/*#ht-contactus-wrap .address-c-box { padding: 3em 1em;}*/
.footer-area .social-profile-icons ul li {
    margin: 12px 5px;
    width: 56px;
    height: 56px;
}
.footer-area .social-profile-icons ul li i {
    font-size: 24px;
    line-height: 56px;
}

}
@media screen and (min-width: 992px) and (max-width: 1299px){
    .counter-area .count-box {  padding: 20px 10px;}
}
@media screen and (min-width: 994px) and (max-width: 1023px){
    #featured-product-section .owl-carousel .owl-nav{display: none !important;}
header .Reg {font-size: 15px;}
.slider_section .title {font-size: 40px;padding: 0;}
.slider_section .sub-title {font-weight: 500;line-height: 25px;padding: 0;margin: 10px 0;}
.slider_section .btn5 { margin-top: 1em;}
.slider_content {top: 50%;width: 845px;left: 58%;}
#about-section .abt-lhsbx {padding-right: 0em ;}
#slider .owl-nav button {display: none;}
.footer-area .social-profile-icons ul li i {font-size: 20px; line-height: 54px;}
.footer-area .social-profile-icons ul li {
    margin: 12px 4px;
    width: 54px;
    height: 54px;
    line-height: 54px;
}
}

@media screen and (min-width: 320px) and (max-width: 1024px){

/*.counter-area .counter-single-area {width: 100%;}*/
header.site-header span.text, .single-header-info span.theme-color{font-size: 11px;}
header.site-header .d-flex, div#navbarNavDropdown {margin: 0 0%;}

.innerpage-whitebox{padding: 1em 0em 1em 0em;}
header.site-header .list-inline.m-a0{padding-left: 0px !important;}header.site-header .text-bx.padding0{padding-left: 15px;}
header.site-header input[type="search"] {width: 100%;}
#ht-contactus-wrap .address-c-box {
    margin: 3em 0 0 0 !important;}
}

@media screen and (max-width: 1000px){

    .toggle-bar{
     display: block;
     position: absolute;
     top: -35px;
     right: 35px;
     width: 50px;
     background: #fe5722;
     cursor: pointer;
     text-align: center;
     color: #fff;
     padding: 20px 0;
     cursor: pointer;
 }
 .toggle-bar span{
    position: absolute;
    top: 50%;
    margin-top: -2px;
    height: 4px;
    left: 8px;
    right: 8px;
    background: #FFF;
    box-shadow: 0 10px 0 0 #FFF, 0 -10px 0 0 #FFF;
}
}
@media (max-width: 1012px){
header .Reg {font-size: 15px;}
#about-section .abt-lhsbx {padding-right: 0em ;}
.slider_section .title {font-size: 35px;line-height: 35px;}
.ht-slide img {height: auto !important;}
.img-slide-responsive {position: absolute;right: 620px;top: -6px;}
.tp-loop-wrap.rs-wave {min-width: 620px !important;}
#slider .owl-nav button{display: none;}
.slider_section a.btn {font-size: 14px;}
.slider-btn {margin-top: 0.7em;}
.header-transparent .sticky-header {padding: 0;}
.top-bar-head {margin-top: 1em;padding: 0;}
}

@media (max-width: 992px){
header .HeaderRbx{background: none !important;}
.slider_section .sub-title{margin: 5px 0;}
.slider_section .btn5 a{margin-left: 0;}
.responsive-header-box{display: none;}
.header-btn .head-button a {float: revert;}
.top-bar-head {position: relative;text-align: center;}
.header-btn .head-button a{display: inline-block;}
#slider .owl-nav button{display: none;}
.slider_section .btn5 { margin-top: 1em;}
.slider_section .title {font-size: 40px ;line-height: 40px;margin-bottom: 0px;}
.slider_content {top: 50%;/*width: 500px;*/margin: 0 auto;left: 0px;right: 0;}
.sf-arrows .sf-with-ul:after{right: 30px !important;}
.ht-site-title, .site-title {font-size: 23px;}

.mobnone{position: relative;display: inline-block;padding: 5px 15px; width:100%;}
.hbtn {display: none;}
/*header .Reg {padding: 0px 0;}*/
.section-title {margin-bottom: 1em !important;}
header .header-right{margin: 0px 0;float: right;}
main#innerpage-box .ht-blog-thumbnail img {height: auto;}
header.site-header ul.hd-contact {margin-left: 0px;padding: 4px 0;}
header .header-text{padding-left: 1em;}
.total-count i {font-size: 16px;padding: 8px;}
.footer-area{padding: 5em 0 2em 0 !important;}
.service-area .single-service-bx:nth-child(2) .service-icon a.services-img, 
.service-area .single-service-bx:nth-child(5) .service-icon a.services-img, 
.service-area .single-service-bx:nth-child(8) .service-icon a.services-img, 
.service-area .single-service-bx:nth-child(11) .service-icon a.services-img {position: relative;}
.h-s-width {width: 60%;}
.contex-about {padding: 1em 0em;}
.call-area .owl-carousel .owl-dots.disabled,
.call-area .owl-carousel .owl-nav.disabled{ display: block;}
.header-contact {padding-right: 0;}
.footer-area .wpcf7:after {left: 18px;top: -35px;right: 0;margin: 0 auto;padding: 0;position: relative;}
.s-footer {
    padding: 0 1em;
}
}

@media screen and (max-width: 990px){
div#postproducts .Postp_titlearea h3 {font-size: 35px;line-height: 45px;}
div#postproducts .Postp_titlearea h3 b {font-size: 40px;}
div#postproducts .Postp_titlearea p {font-size: 13px;line-height: 20px;}
div#postproducts .ppost-area .postp_sarea .Postp_titlearea {padding: 1em 0;}
div#postproducts .postp-area-btn a {font-size: 12px;padding: 8px 35px;}
div#postproducts .postp-area-btn {margin-top: 1em;}
.page-main-header {
    padding:0 !important;
}
}

@media screen and (max-width: 768px){
#featured-product-section .owl-carousel .owl-nav{display: none !important;}
.service-area .owl-nav, .service-area .owl-carousel .owl-nav.disabled{display: none;}
#innerpage-box .single-team-img img {height: 300px;}
main#innerpage-box h6.faq-title{padding: 10px 75px 8px 17px !important;}
.single-productpage .innerpage-whitebox,
.single-productpage #sidebars {margin-top: 00px;}
.ht-footer.ht-footer1,
.ht-footer.ht-footer2,
.ht-footer.ht-footer3,
.ht-footer.ht-footer4 {width: 100% !important;}

.ht-main-navigation li{padding-right: 00px;}
img.ht-page-header-img{height: 275px;}
.ht-site-title a, .site-title a{top: 0 !important;}
.ht-slide-cap-title.animated.fadeInDown{font-size: 22px;}
.ht-slide-cap-desc.animated.fadeInDown{font-size: 13px;line-height: 20px;}
#quote-carousel .carousel-control.right {right: 0;top: 140px;}
#quote-carousel .carousel-control.left {left: -7px;top: 140px;}
#quote-carousel .carousel-control {margin-top: -50px;}
.ht-slide-cap-descmore {margin-top: 5px;}
.section-title h2{font-size: 32px;}
body.admin-bar .navigation {top: 45px;}
.top-bar-head {width: 100%;}
/*#innerpage-box .ts-area-c {
    width: 100%;
    padding: 30px  0 0 0;
}*/
#appointment h5 {   font-size: 27px;}
#appointment .appbtn a {  font-size: 20px;}
#innerpage-box .ts-img {
    width: 100%;
}
}

@media screen and (min-width: 810px) and (max-width: 825px){
    .hamburger-menus {
        right: -25px;
    }
}

@media screen and (max-width: 600px){
#appointment h5 {font-size: 22px;}
#featured-product-section .product-grid {
    margin: 2em auto !important ;
}
.faq_bm .faq-heading, .faq_tp .faq-heading{
    font-size: 35px;
}
}
@media screen and (min-width: 320px) and (max-width: 425px){
body {position: inherit; }
#ht-contactus-wrap .Address_area { padding: 0em 0em;}
.slider_section .btn5{margin-top:6px;}
.slider_section .btn5 a {font-size: 12px;}
.slide-btn{bottom: 35px !important;}
#features-section .features-content {padding: 10px;}
#features-section .features-content h3 {font-size: 22px;}
/*#features-section .sec-icn span {font-size: 50px;}*/
.service-area .readmore a{padding: 10px 15px;font-size: 12px;}
.service-area .service-title-box {padding: 1em 1em;}
.service-area .inner-area-title{font-size: 16px;}
.logo{bottom: 0;}
.counter-area .cd-num{border:none;}
#innerpage-box .contsec img {height: 300px;}
.faq_bm .faq-heading, .faq_tp .faq-heading{
    font-size: 28px;
    letter-spacing: 3px;
}
#innerpage-box .single-project .boxinn { 
    padding: 1em 1em 1em 1em;
}
}

@media screen and (max-width: 768px){
.slider_section p{padding-left: 10px;}
.slider_section .extimg img.ex{max-width: 22%;}
.slide-btn{bottom: 110px;}
.slide-btna a.btn{font-size: 14px;padding: 12px 20px;}
.woocommerce ul.cart_list li, .woocommerce ul.product_list_widget li{margin-bottom: 10px;}
.woocommerce .widget_price_filter .price_slider_amount .button{margin-bottom: 15px;}
.ht-main-navigation a{padding: 8px 17px;}
.ht-section-title,
.ht-section-tagline{width: auto;}
.ht-top-footer{margin-left: 0;}
.ht-footer{width: auto;margin: 0 0 30px;float: none;}
.ht-footer:last-child{margin-bottom: 0;}
.ht-slide-cap-descmore a{font-size: 2vw;}
.ht-slide-cap-title{font-size: 24px;font-size: 4vw;margin-bottom: 0px;}
#primary{width: auto !important;float: none !important;margin-bottom: 30px;}
#secondary{width: auto !important;float: none !important;margin-bottom: 30px;}

div#postproducts .ppost-area .postp_sarea {margin-bottom: 2em;}
div#postproducts{padding: 2em 0 0 !important;}
main#innerpage-box .Address_area h4, main#innerpage-box .social_area h4 {font-size: 30px;}
#ht-contactus-wrap .contact_l_area span {font-size: 22px;}
#ht-contactus-wrap .contact-info {margin-top: 0px;font-size: 14px;}
#appointment .app-rhsbx {  padding: 2em 0em 1em 1em;}
.pd-r {padding: 2em 0;padding-right: 2em;}
.pd-l {padding: 2em 0;padding-left: 2em;}
.social_area i.fa {right: 49%;}
#footer.footer-area .widget-title {
    font-size: 25px;
}
}

@media screen and (min-width: 669px){
#ht-contactus-wrap .Address_area i.fa.fa-long-arrow-up , 
#ht-contactus-wrap .Address_area i.fa.fa-long-arrow-down {display: none;}
#ht-contactus-wrap .Address_area i.fa.fa-long-arrow-right, 
#ht-contactus-wrap .Address_area i.fa.fa-long-arrow-left {
    display: block;
}
}

@media screen and (max-width: 668px){
div#postproducts .ppost-area .postp_sarea:nth-child(2n) .Postp_titlearea {float: none;}
/*#ht-contactus-wrap .contact_area {padding: 0px 0em 0;}*/
#ht-contactus-wrap .address-box {text-align: center;}
.social_area {text-align: center;}
#ht-contactus-wrap .Address_area i.fa.fa-long-arrow-right, 
#ht-contactus-wrap .Address_area i.fa.fa-long-arrow-left {display: none;}
#ht-contactus-wrap .Address_area i.fa.fa-long-arrow-up , 
#ht-contactus-wrap .Address_area i.fa.fa-long-arrow-down {display: block;}
#ht-contactus-wrap .contact_l_area span {font-size: 35px;}
#ht-contactus-wrap .contact-info {font-size: 15px;}
.social_area i.fa {right: 45%;}
.mg-3 {margin: 1em 0 0;}
.pd-r {padding: 0em 0;}
.pd-l {padding: 0em 0;}
main#innerpage-box .Address_area h4, 
main#innerpage-box .social_area h4, 
.page-template-contact-template main#innerpage-box .Address_area h4, 
.page-template-contact-template main#innerpage-box .social_area h4 {font-size: 28px !important;} 
#ht-contactus-wrap .col-md-8.col-sm-8.col-xs-12 {padding: 0;}
#ht-contactus-wrap .address-box {text-align: center;}
#ht-contactus-wrap .contact-page-form {padding: 0 1em 1em;}
/*#ht-contactus-wrap .contact-sm-links li a span {
    width: 45px;
    height: 45px;
    font-size: 20px;
    line-height: 45px;
}*/
}

@media screen and (max-width: 580px){
.slider_section .bx-wrapper .bx-controls-direction a,
.ht-testimonial-wrap .bx-wrapper .bx-controls-direction a{display: none;}    
.ht-team-counter-wrap{margin-left: 0;}
.ht-counter{width: 75%;float: none;margin: 0 auto 60px;}
.ht-portfolio{
    width: 50%;
    padding: 4px;
}
#innerpage-box .inser{width: 100% !important;}
}

@media screen and (min-width: 320px) and (max-width: 768px){
.logobox img {padding-top: 0 !important;}
.section-title {margin-bottom: 1.5em !important;}
header .header-contact {text-align: center;}
.slider_section .sub-title {padding: 0px;font-size: 14px;line-height: 18px;width: 320px;}
.main-dispaly.padding0 {padding-left: 20px;}
#slider .owl-nav button{display: none;}
.slider_section .extimg{width: 100%;}
#innerpage-box .ts-area-c p{font-size: 16px;}
header.site-header button.header-btn{float: left;}
.top-bar-head {position: relative;}
.header.site-header ul.sub-menu li a:hover{padding-left: 15px;padding-right: 15px;}
.navigation .mainmenu li ul li{top: auto;}
.navigation .mainmenu li:hover > ul{position: relative;top: auto;}
.navigation .mainmenu li:hover ul li:hover > ul{position: relative;left: 0;}
.navigation .mainmenu li{bottom: auto;}
.navigation .mainmenu li ul li a{padding: 5px 0 5px 25px;}
.navigation .mainmenu li a{text-align: left;}
.navigation .mainmenu li:hover > ul{
    width: 100%;
}
}

@media screen and (min-width: 320px) and (max-width: 768px){
header.site-header .main-dispaly.padding0 {padding-right: 20px;}
.navigation .mainmenu {right: 0;padding-left: 0;}
.ht-section-title:after {width: 65%;}
.woocommerce div.product .woocommerce-tabs ul.tabs{padding-top: 10px;}
.woocommerce div.product .woocommerce-tabs ul.tabs li a{font-size: 18px;}
.woocommerce ul.products li.product, 
.woocommerce-page ul.products li.product, 
.woocommerce .related ul.products li.product, 
.woocommerce-page .related ul.products li.product{margin-bottom: 30px !important;}

.sf-arrows .sf-with-ul:after{right: 40px;}
.navigation .mainmenu li a{box-shadow: none;}
.ti-home:before{left: 15px;top: 12px;}
.is-fixed .main-bar ul.sub-menu {margin-top: 0 ;}
.navigation .mainmenu li a,
header.site-header ul.sub-menu li a{padding: 5px 0 5px 10px;}
.ht-menu.clearfix ul {margin-left: 0;}
header.site-header ul.sub-menu li a {padding: 5px 45px;}
ul.sub-menu{
    position: relative;
    box-shadow: none;
    border-top: 0;
    margin-top: 0px;
    margin-left: 0;
    padding: 0;
}
}
@media screen and (min-width: 320px) and (max-width: 767px){

header.site-header ul.hd-contact {
    padding: 0 0 20px 0 !important;
}
.blog-area .blog-comm{padding: 0;line-height: 25px;}
.header-share {float: revert !important;}
header .Reg li {display: block;margin: 5px 0;}


}
@media only screen and (max-width: 767px) {
    .blocks-gallery-grid img {   height: 150px !important;}
#team .team-social-icon {   display: block;}
#features-section .features-content{padding: 20px 0;}
#features-section .features-inn {text-align: center;}
.counter-area .count-box {   padding: 30px 10px;}
#ht-contactus-wrap .contact_area {  text-align: center;}
    .slide-content p{
        display:none;
    }
    .slide-content-box{
        text-align:center;
    }
    .owl-slider.owl-theme .owl-dots {
        bottom: 180px;
        margin-top: 0;
        position: absolute;
        right: auto;
        text-align: center;
        top: auto;
        transform: translateY(-50%);
        width: 100%;
    }
    .site-header .navbar-toggler, 
    .site-header .is-fixed .navbar-toggler {
        margin: 22px 0 22px 10px;
        box-shadow: none;
    }
    .slide-content .button-lg {
        font-size: 13px;
        padding: 10px 15px;
    }
    .text-white .site-button.outline {
        padding: 8px 15px;
    }
    .abuot-box {
        background: rgba(0, 0, 0, 0) none repeat scroll 0 0;
        border-radius: 0;
        box-shadow: none;
        margin-top: 0;
        padding: 0;
        position: relative;
        width: 100%;
        z-index: unset;
    }
    .abuot-box [class*="col-"]{ 
        padding:0;
    }
    .choseus-tabs .title-head {
        display: unset;
    }
    .chosesus-content .abuot-box {
        background-color: rgba(255, 255, 255, 0.05);
        border-radius: 5px;
        left: auto;
        margin-top: 0;
        padding: 20px;
    }
    .chosesus-content .abuot-box p{
        color: #ffffff;
        font-size: 14px;
        opacity: 0.8;
    }
    .choses-info-content [class*="col-"] {
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    }
    .inquiry-form{
        margin-top:0;
    }
    .contact-page{
        padding-bottom: 50px;
        padding-top: 100px;
    }
.ht-newsletter-member-wrap input[type="submit"] {  
    position: relative;
    right: 0;
}
}
@media screen and (min-width: 320px) and (max-width: 768px){

.footer-area .bottom-area{padding: 20px 0 !important;}

.footer-area .gallery-item{max-width: 50%;}
.footer-bottom-area ul li { margin: 0 15px;}
.footer-bottom-area.text-right {text-align: center;}
.footer-text {
    text-align: center;
    margin-bottom: 0;
}
.slider_section .layer-2{top: 12.9%;}
.slider_section .layer-8 {
top: 11.5%;
}

.header-transparent .is-fixed .main-bar h1.ht-site-title {
position: relative;
top: 60px;
text-align: center;
}

header.site-header span.text, .single-header-info span.theme-color {
font-size: 14px;
}
.slider_section::after{ left: -4%; bottom: -20.8%; width: 104%; }

.bx-controls-direction { display: none;  }
.slider_section .layer-3{ top: 50.9%; }

header.site-header .d-flex, div#navbarNavDropdown {margin: 0 0% !important; }
div#navbarNavDropdown{  margin: 0%; }

.slider_section .box{ right: 0; }
.slider_section .box-hover, .box-hover-2, .slider_section .box{ width: 700px; }
.ht-site-description{padding-top: 0; }

.header-top-container{ padding:0px !important; }

.header-transparent .is-fixed .main-bar{position: static !important;}
.header-transparent .is-fixed .main-bar h1.ht-site-title,
.header-transparent .is-fixed .main-bar p.ht-site-description { display: block;}
.navigation .mainmenu li a:after{ content: none; }
.navigation .mainmenu li ul li{ box-shadow: none; padding-left: 0; }
.sf-arrows .sf-with-ul:before{ right: 40px !important; }

.header-seo.header-transparent .is-fixed .main-bar {background-color: transparent !important;padding: 0;}
.top-bar-head { width: 100%;}
div#navbarNavDropdown li.current_page_item a:after, .current_page_item > a:after{width: 0;}
.header-seo.header-transparent .is-fixed .main-bar img {
    position: relative;
    top: 80px;
}
.header-seo.header-transparent .is-fixed .main-bar .resp_header_logo{display: block;}
}

@media screen and (max-width: 747px){
.tp-loop-wrap.rs-wave {
    min-width: 617px !important;
}
.img-slide-responsive {
    position: absolute;
    right: 617px;
}
}

@media screen and (max-width: 640px){
.head-menu {
    position: relative;
    width: 100%;
    padding: 4px 16px;
}
.total-count .count {
    top: 15px;
    right: 14px;
}

.total-count {
    margin-top: 10px;
}
.header-btn .head-button a {
    padding: 10px 18px;
    font-size: 16px;
}
.slider_section .owl-theme .owl-nav {
    display: none;
}
.owl-theme .owl-dots .owl-dot span {
    width: 15px !important;
    height: 15px !important;
    margin: 5px 14px;
}
.slider_section .owl-theme .owl-dots .owl-dot span:before {
    font-size: 10px;
}
.slider_section .owl-theme .owl-dots .owl-dot.active span:before {
    width: 15px;
}
.margin-sec {
    margin: 0 3em;
}

a.call-num {
    font-size: 18px;
}
.call-label {
    font-size: 18px;
}

.features-area .fs-in-area a.icon {
    padding-top: 8px;
}

.featured-single-area {
    margin: 0 3em;
}
.features-area .spring-1 {
    left: -40px;
}
.footer-area .widget {
    margin-bottom: 1.5em;
}
footer#footer.footer-area .widget-title {
    margin-bottom: 1em;
}

.h-s-width {
    width: 80%;
}
.contact-content .phone {
    font-size: 26px;
    line-height: 32px;
}

.single-footer-1, .single-footer-2, .single-footer-3,.single-footer-4 , .single-footer-5{
    width: 100%;
}
#innerpage-box .item.innertest-item{
    width: 100%;
}
#innerpage-box .ts-area-content {
    padding: 1em 0em 0em 0em;
}
#innerpage-box .ts-area-single{
    margin: 0;
}
}

@media screen and (min-width: 400px) and (max-width: 600px){
.slider_content{width: 400px;}
.slider_section .title {font-size: 35px;line-height: 35px;padding-bottom: 5px;}
.slider_section a.btn {font-size: 10px;}
.hamburger-menus {top: 0px; }
/*#ht-contactus-wrap .Address_area {padding: 5em 3em;}*/
.footer-area .overlay{padding: 0!important;}
.s-footer {margin-bottom: 1em;}
}

@media screen and (min-width: 320px) and (max-width: 399px){
.slider_content{
    width: 300px;
}
}
@media screen and (min-width: 320px) and (max-width: 475px){

.ht-site-description{
    font-size: 12px;
}
.slider_section .title {
    font-size: 20px;
    line-height: 23px;
}
.slide-btna a.btn{
    padding: 8px 20px;
    font-size: 10px;
}

.tp-loop-wrap.rs-wave {
    min-width: 300px !important;
}
   .img-slide-responsive {
    right: 300px;
}

ul.hd-contact li {
padding-right: 0;
}
    main#innerpage-box #blog-box .socialMedia ul li a.site-button{
        width: 40px;
        line-height: 40px;
        height: 40px;
    }
    main#innerpage-box #blog-box .socialMedia ul li {
        margin-left: -40px;
    }
    .page_featured {
        margin: 0 2em;
        margin-bottom: 1em;
        padding: 3em 4em;
    }
    body.page-template-default main#innerpage-box h1, .ht-main-title, #ht-contactus-wrap h1 {
    font-size: 25px !important; 
}
}
@media screen and (min-width: 320px) and (max-width: 475px){
    .woocommerce ul.products li.product .woocommerce-loop-category__title, 
    .woocommerce ul.products li.product .woocommerce-loop-product__title, 
    .woocommerce ul.products li.product h3{
        font-size: 20px !important;
    }
    .single-productpage .innerpage-whitebox{
        padding: 0;
    }
    .woocommerce-page.columns-3 ul.products li.product, 
    .woocommerce.columns-3 ul.products li.product {
        width: 30.75%;
        width: 100% !important;
    }
    #blog-box h2{
        font-size: 22px !important;
    }
    div#blog-box.innerpage-whitebox {
        padding: 0em 0em 2em 0em;
    }
    .slider_section .char{
        font-size: 12px;
        height: 20px;
    }
    .slider_section p{
        width: 90%;
        font-size: 12px;
        line-height: 20px !important;
        padding: 0;

    }
    .ht-slide-cap-desc{
       font-size: 28px;
       line-height: 35px;
       margin-bottom: 10px;
   }
    .woocommerce #content div.product .woocommerce-tabs ul.tabs li, 
    .woocommerce div.product .woocommerce-tabs ul.tabs li, 
    .woocommerce-page #content div.product .woocommerce-tabs ul.tabs li, 
    .woocommerce-page div.product .woocommerce-tabs ul.tabs li {
        margin-bottom: 10px;
    }
}
@media screen and (min-width: 320px) and (max-width: 475px){

.footer-area .footerlayer-3{bottom: 10%;}
.footer-area .footerlayer-2{bottom: 40%;}
.footer-area .footerlayer-1{top: 2%;left: 35.3%;}
.footer-bottom-area ul li { margin: 0 10px;}
.footer-text, .footer-area li{font-size: 14px;}

.slider_section i.fa{font-size: 10px;}
.slider-social-icon > span{letter-spacing: 0;}
.slider_section .layer-3 img {
width: 30px;
}
 .header-transparent .is-fixed .main-bar h1.ht-site-title{top: 35px;}
.resp_head_box{margin-bottom: 0;}
.header-seo.header-transparent .is-fixed .main-bar img { top: 55px;}
.header-seo.header-transparent .is-fixed .main-bar img { top: 50px;}
div#sb-search {margin-left: 0;}
header.site-header input[type="search"]{width: 100%;}
header.site-header span.sb-icon-search,
header.site-header input[type="submit"]{right: 0;}

.slider_section::after{width: 0;}

.slider_section div.slider-social-icon { left: 90px; top: 88%;    transform: rotate(0deg);}
.social-icon-container a {font-size: 8px;padding: 0 2px; margin: 0 4px;}
.slider_section .layer-5 img,.slider_section .layer-8 img{ width: 25px;}

.slider_section .layer-6 img, .slider_section .layer-4 img{width: 15px; }
.slider_section .layer-3 img{ width: 40px; }
.slider_section .layer-8{top: 10.5%;}
.slider_section .layer-6 { bottom: 10%;right: 62.4%;}
.slider_section .layer-5 { bottom: 92px; }
.slider_section .layer-4 { top: 73.5%; }
.slider_section .box-hover, .box-hover-2, .slider_section .box { width: 380px;}
.slider_section .btn:hover:before{top: 6px; }
.slider_section .box-hover, .box-hover-2{height: 340px;}
.slider_section .box-hover, .box-hover-2{ margin-top: 0; }

.slider_section .btn{font-size: 14px; padding: 8px 32px 8px 32px;}
.slider_section .box{ height: 325px; right: 0%;}

.single-header-info { text-align: center; padding: 0;}

}


@media screen and (min-width: 320px) and (max-width: 375px){
.slider_section .box-hover, .box-hover-2, .slider_section .box { width: 360px; }
.slider_section .box-hover, .box-hover-2{height: 305px; }
.slider_section .box{ height: 300px;}
.woocommerce-error a.button, 
.woocommerce-info a.button, 
.woocommerce-message a.button{
    /*margin-right: -30px;*/
    padding: 8px 9px;
    font-size: 13px;
}
.slider_section .title {
      font-size: 15px;
}
.slider_section .sub-title {
    font-size: 13px;
    line-height: 13px;
}
.slider-btn {
    margin-top: 0.5em;
}
.ht-site-title, .site-title {
    font-size: 16px;
}
.ht-site-description {
    font-size: 10px;
}
.navicon {
    top: -60px !important;
}
.page_featured {
    margin-bottom: 1em;
    padding: 2em 4em;
}

/*.blog-area .blog-thumbnail img{*/
/*    height: 315px;*/
/*}*/

main#innerpage-box .ht-blog-thumbnail img {
    height: auto;
}
.total-count .count {
    right: 8px;
}
.service-area .single-service-bx .service-icon a.services-img img {
    height: 240px;
}
/*.blog-area .blog-thumbnail img {*/
/*    height: 205px;*/
/*}*/

@media screen and (min-width: 320px) and (max-width: 360px){

/*.blog-area .blog-thumbnail img{*/
/*    height: 260px;*/
/*}*/

main#innerpage-box .ht-blog-thumbnail img { height: auto;}
.margin-sec {margin: 0 2em;}
.service-area .single-service-bx .service-icon a.services-img img {height: 215px;}
.contact-small-box span.fa {  font-size: 40px;}
.contact-content {  font-size: 11px;}
.contact-content .phone { font-size: 17px; line-height: 28px;}
.ht-inquiry-member-wrap div.wpcf7 .ajax-loader {  display: contents;}
.inner-page-gallery img {
    height: 200px;
}
}

@media screen and (min-width: 320px) and (max-width: 320px){
    .slider_section div.slider-social-icon{left: 45px;}
    .header-transparent .is-fixed .main-bar h1.ht-site-title {
    top: 45px;
}

.slider_section .box-hover, .box-hover-2, .slider_section .box {width: 300px;}
.slider_section .box-hover, .box-hover-2 { height: 270px; }
.slider_section .box {height: 275px;}

.woocommerce div.product .woocommerce-tabs ul.tabs li a { font-size: 15px;}

.ht-slide-cap-desc { line-height: 15px;}
.woocommerce div.product .woocommerce-product-rating {line-height: 5 !important;}
.star-rating {padding-top: 9px;}

.toggle-bar {right: 25px !important;}
.slider_section .title {
    font-size: 15px;
    margin-bottom: 0;
}

.slider_section .sub-title {
    /*line-height: 15px;*/
      font-size: 11px;
}

.slider_section a.btn {
    font-size: 10px;
    padding: 6px 15px;
}

.page_featured {
    margin-bottom: 0em;
}
.total-count .count {
    right: 0px;
}
.blog-area .blog-area-wrap {
    margin: 0 1em;
}
/*.blog-area .blog-thumbnail img {*/
/*    height: 200px !important;*/
/*}*/
}
main#innerpage-box .tagcloud a {
    border-radius: 5px;
    box-shadow: 0px 6px 10px -4px rgba(0,0,0,0.4);
    padding: 7px;
    display: inline-block;
}
div#secondary select option {
    background: transparent;
}

body.home.blog .page-main-header img {
    display: none;
}

.woocommerce ul.cart_list li, 
.woocommerce ul.product_list_widget li{
    padding: 5px 0;
}
h1.product_title.entry-title {
    word-break: break-word;
}
.select2-container--default .select2-selection--single,
.woocommerce .quantity .qty,
div#payment,
#coupon_code{
    background: transparent !important;
}
#content-box li.blocks-gallery-item:before {
    content: none;
}
div#content-box li.blocks-gallery-item figure img {
    padding: 5px;
    margin-bottom: 0;
}
div#content-box li.blocks-gallery-item figure img:hover,
div#sitemap-box .sitemap-posts-box img:hover{
    opacity: 0.7;
}
main#innerpage-box ul.children {
    border: 0;
}
.woocommerce-MyAccount-content .woocommerce-info {
    background: transparent !important;
}
.woocommerce button.button:disabled, 
.woocommerce button.button:disabled[disabled]{
    opacity: 1 !important;
}